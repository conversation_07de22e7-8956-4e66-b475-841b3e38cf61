"""
测试简化版MCP服务器
"""
import asyncio
import logging
import json
from fastmcp import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_simple_server():
    """测试简化版服务器功能"""
    
    server_url = "http://localhost:8000/sse"
    
    try:
        logger.info("🔍 连接到简化版MCP服务器...")
        
        async with Client(server_url) as client:
            logger.info("✅ 连接成功!")
            
            # 测试1: 列出可用工具
            tools = await client.list_tools()
            logger.info(f"📋 可用工具: {[tool.name for tool in tools]}")
            
            # 测试2: 测试主要接口
            test_url = "https://httpbin.org/html"
            logger.info(f"🌐 测试抓取和分析: {test_url}")
            
            result = await client.call_tool("scrape_and_analyze", {
                "url": test_url,
                "max_images": 3
            })
            
            if result:
                data = json.loads(result[0].text)
                logger.info(f"✅ 测试成功!")
                logger.info(f"   - 网页标题: {data.get('title', 'N/A')}")
                logger.info(f"   - 文本长度: {data.get('text_content', {}).get('length', 0) if isinstance(data.get('text_content'), dict) else len(str(data.get('text_content', '')))}")
                logger.info(f"   - 图片总数: {data.get('total_images', 0)}")
                logger.info(f"   - 分析图片: {data.get('analyzed_images', 0)}")
                
                if data.get('image_analyses'):
                    logger.info("📸 图片分析结果:")
                    for i, img_analysis in enumerate(data['image_analyses'][:2]):  # 只显示前2个
                        analysis_text = img_analysis.get('analysis', {}).get('analysis', 'N/A')
                        logger.info(f"   图片{i+1}: {analysis_text[:100]}...")
            else:
                logger.warning("❌ 没有返回结果")
            
            logger.info("🎉 所有测试完成!")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        logger.info("请确保服务器正在运行: python main_simple.py")

if __name__ == "__main__":
    asyncio.run(test_simple_server())
