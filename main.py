"""
MCP Web Scraper Service with SSE Interface
Provides web scraping and image analysis capabilities using Doubao vision model
"""
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from fastmcp import FastMCP
from config import get_settings
from web_scraper import WebScraper
from image_analyzer import DoubaoImageAnalyzer

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mcp_server.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Set specific loggers
logging.getLogger('fastmcp').setLevel(logging.DEBUG)
logging.getLogger('playwright').setLevel(logging.INFO)
logging.getLogger('httpx').setLevel(logging.INFO)

# Initialize settings
settings = get_settings()

# Create FastMCP server
mcp = FastMCP(
    name="Web Scraper with Vision Analysis",
    description="A comprehensive web scraping service with image analysis using Doubao vision model",
    dependencies=["playwright", "pillow", "beautifulsoup4", "requests", "httpx", "python-dotenv"]
)

@mcp.tool()
async def scrape_and_analyze(url: str, max_images: int = 5) -> Dict[str, Any]:
    """
    一次性抓取网页并分析图片，返回完整的文本和图片解析数据

    Args:
        url: 要抓取的网页URL
        max_images: 最大分析图片数量，默认5张

    Returns:
        包含网页文本、图片分析结果的完整数据
    """
    try:
        logger.info(f"🚀 开始抓取和分析: {url}")
        logger.debug(f"参数: max_images={max_images}")

        # 1. 抓取网页
        logger.info("📄 开始抓取网页内容...")
        async with WebScraper() as scraper:
            scrape_result = await scraper.scrape_webpage(url)

        if not scrape_result['success']:
            logger.error(f"❌ 网页抓取失败: {scrape_result.get('error', '未知错误')}")
            return {
                'success': False,
                'url': url,
                'error': f"抓取失败: {scrape_result.get('error', '未知错误')}"
            }

        logger.info(f"✅ 网页抓取成功 - 标题: {scrape_result['title']}")
        logger.info(f"📊 文本长度: {scrape_result['text_length']} 字符")
        logger.info(f"🖼️ 发现图片: {len(scrape_result['images'])} 张")

        # 2. 分析图片
        image_analyses = []
        if scrape_result['images']:
            logger.info(f"🔍 开始分析图片...")
            analyzer = DoubaoImageAnalyzer()

            # 限制分析的图片数量
            images_to_analyze = scrape_result['images'][:max_images]
            logger.info(f"📸 将分析 {len(images_to_analyze)} 张图片 (共 {len(scrape_result['images'])} 张)")

            for i, img_info in enumerate(images_to_analyze, 1):
                if img_info.get('local_path'):
                    logger.debug(f"分析第 {i} 张图片: {img_info.get('filename', 'unknown')}")

                    analysis = await analyzer.analyze_image(
                        img_info['local_path'],
                        prompt="详细描述这张图片的内容，包括主要元素、文字、颜色和布局。",
                        context=f"这张图片来自网页: {url}"
                    )

                    image_analyses.append({
                        'image_index': i,
                        'filename': img_info.get('filename', ''),
                        'url': img_info['url'],
                        'local_path': img_info['local_path'],
                        'analysis': analysis
                    })

                    if analysis.get('success'):
                        logger.info(f"✅ 第 {i} 张图片分析成功")
                    else:
                        logger.warning(f"⚠️ 第 {i} 张图片分析失败: {analysis.get('error', '未知错误')}")
        else:
            logger.info("ℹ️ 网页中没有发现图片")

        # 3. 返回完整结果
        result = {
            'success': True,
            'url': url,
            'title': scrape_result['title'],
            'text_content': scrape_result['text_content'],
            'main_content': scrape_result['main_content'],
            'meta_description': scrape_result['meta_description'],
            'total_images': len(scrape_result['images']),
            'analyzed_images': len(image_analyses),
            'image_analyses': image_analyses,
            'timestamp': scrape_result['timestamp']
        }

        successful_analyses = sum(1 for img in image_analyses if img['analysis'].get('success'))
        logger.info(f"🎉 分析完成 - URL: {url}")
        logger.info(f"📝 文本: {len(scrape_result['text_content'])} 字符")
        logger.info(f"🖼️ 图片: {successful_analyses}/{len(image_analyses)} 张成功分析")

        return result

    except Exception as e:
        logger.error(f"💥 抓取分析错误 {url}: {e}", exc_info=True)
        return {
            'success': False,
            'url': url,
            'error': str(e)
        }

# 删除单独的图片分析工具，只保留主要接口

@mcp.tool()
async def analyze_multiple_images(
    image_paths: List[str],
    prompt: str = "请详细描述这张图片的内容。",
    context: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Analyze multiple images using Doubao vision model
    
    Args:
        image_paths: List of image file paths
        prompt: Analysis prompt for all images
        context: Additional context for analysis
        
    Returns:
        List of analysis results for each image
    """
    try:
        logger.info(f"Starting analysis for {len(image_paths)} images")
        
        # Filter existing files
        existing_paths = [path for path in image_paths if Path(path).exists()]
        missing_paths = [path for path in image_paths if not Path(path).exists()]
        
        if missing_paths:
            logger.warning(f"Missing image files: {missing_paths}")
        
        if not existing_paths:
            return [{
                'success': False,
                'error': 'No valid image files found',
                'image_paths': image_paths
            }]
        
        async with DoubaoImageAnalyzer() as analyzer:
            results = await analyzer.analyze_multiple_images(existing_paths, prompt, context)
        
        # Add results for missing files
        for missing_path in missing_paths:
            results.append({
                'success': False,
                'error': f"Image file not found: {missing_path}",
                'image_path': missing_path
            })
        
        successful_count = sum(1 for r in results if r.get('success', False))
        logger.info(f"Analyzed {successful_count}/{len(image_paths)} images successfully")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in analyze_multiple_images: {e}")
        return [{
            'success': False,
            'error': str(e),
            'image_paths': image_paths
        }]

@mcp.tool()
async def scrape_and_analyze_webpage(
    url: str,
    image_analysis_prompt: str = "请详细分析这张图片，并说明它在网页中的作用和意义。",
    analyze_all_images: bool = True,
    max_images: Optional[int] = 5
) -> Dict[str, Any]:
    """
    Scrape a webpage and analyze its images with context
    
    Args:
        url: The URL to scrape
        image_analysis_prompt: Prompt for image analysis
        analyze_all_images: Whether to analyze all images or just the first few
        max_images: Maximum number of images to analyze (None for all)
        
    Returns:
        Dictionary containing webpage content and image analysis results
    """
    try:
        logger.info(f"Starting comprehensive analysis for: {url}")
        
        # First scrape the webpage
        async with WebScraper() as scraper:
            scrape_result = await scraper.scrape_webpage(url)
        
        if not scrape_result['success']:
            return {
                'success': False,
                'url': url,
                'error': f"Failed to scrape webpage: {scrape_result.get('error', 'Unknown error')}",
                'scrape_result': scrape_result
            }
        
        images = scrape_result.get('images', [])
        if not images:
            return {
                'success': True,
                'url': url,
                'scrape_result': scrape_result,
                'image_analyses': [],
                'message': 'No images found on the webpage'
            }
        
        # Determine which images to analyze
        if not analyze_all_images and max_images:
            images_to_analyze = images[:max_images]
        elif max_images:
            images_to_analyze = images[:max_images]
        else:
            images_to_analyze = images
        
        logger.info(f"Analyzing {len(images_to_analyze)} out of {len(images)} images")
        
        # Analyze images with webpage context
        async with DoubaoImageAnalyzer() as analyzer:
            image_analyses = []
            
            for img_info in images_to_analyze:
                image_path = img_info['local_path']
                
                # Create context from webpage and image metadata
                context = f"""
网页URL: {url}
网页标题: {scrape_result.get('title', '')}
图片原始URL: {img_info.get('original_url', '')}
图片Alt文本: {img_info.get('alt_text', '')}
图片Title: {img_info.get('title', '')}

网页主要内容摘要:
{scrape_result.get('main_content', '')[:1000]}...
                """.strip()
                
                analysis = await analyzer.analyze_image_with_webpage_context(
                    image_path, 
                    scrape_result.get('main_content', ''),
                    url
                )
                
                # Add image metadata to analysis
                analysis['image_info'] = img_info
                image_analyses.append(analysis)
        
        successful_analyses = sum(1 for a in image_analyses if a.get('success', False))
        
        result = {
            'success': True,
            'url': url,
            'scrape_result': scrape_result,
            'image_analyses': image_analyses,
            'summary': {
                'total_images_found': len(images),
                'images_analyzed': len(images_to_analyze),
                'successful_analyses': successful_analyses,
                'text_length': scrape_result.get('text_length', 0)
            }
        }
        
        logger.info(f"Completed comprehensive analysis for {url}: {successful_analyses}/{len(images_to_analyze)} images analyzed")
        return result
        
    except Exception as e:
        logger.error(f"Error in scrape_and_analyze_webpage: {e}")
        return {
            'success': False,
            'url': url,
            'error': str(e)
        }

@mcp.tool()
async def analyze_webpage_complete(
    url: str,
    max_images: int = 5,
    image_prompt: str = "请详细描述这张图片的内容，包括主要元素、颜色、布局和任何文字信息。同时分析图片在网页中的作用。"
) -> Dict[str, Any]:
    """
    一次请求完成网页分析 - 获取文本内容并分析所有图片

    Args:
        url: 要分析的网页URL
        max_images: 最多分析的图片数量 (默认5张)
        image_prompt: 图片分析提示词

    Returns:
        包含网页文本和图片分析的完整结果
    """
    try:
        logger.info(f"开始完整网页分析: {url}")

        # 第一步：抓取网页内容和图片
        async with WebScraper() as scraper:
            scrape_result = await scraper.scrape_webpage(url)

        if not scrape_result['success']:
            return {
                'success': False,
                'url': url,
                'error': f"网页抓取失败: {scrape_result.get('error', '未知错误')}",
                'scrape_result': scrape_result
            }

        # 准备返回结果
        result = {
            'success': True,
            'url': url,
            'webpage_info': {
                'title': scrape_result.get('title', ''),
                'meta_description': scrape_result.get('meta_description', ''),
                'text_content': scrape_result.get('main_content', ''),
                'text_length': scrape_result.get('text_length', 0),
                'total_images_found': len(scrape_result.get('images', []))
            },
            'image_analyses': [],
            'summary': {
                'images_analyzed': 0,
                'successful_analyses': 0,
                'analysis_time': 0
            }
        }

        # 第二步：分析图片（如果有）
        images = scrape_result.get('images', [])
        if images:
            # 限制分析的图片数量
            images_to_analyze = images[:max_images]
            logger.info(f"开始分析 {len(images_to_analyze)} 张图片")

            import time
            start_time = time.time()

            async with DoubaoImageAnalyzer() as analyzer:
                # 并发分析所有图片
                analysis_tasks = []

                for img_info in images_to_analyze:
                    # 为每张图片创建上下文
                    context = f"""
网页信息:
- URL: {url}
- 标题: {scrape_result.get('title', '')}
- 描述: {scrape_result.get('meta_description', '')}

图片信息:
- 原始URL: {img_info.get('original_url', '')}
- Alt文本: {img_info.get('alt_text', '')}
- Title属性: {img_info.get('title', '')}

网页主要内容摘要:
{scrape_result.get('main_content', '')[:800]}...
                    """.strip()

                    # 创建分析任务
                    task = analyzer.analyze_image(
                        img_info['local_path'],
                        image_prompt,
                        context
                    )
                    analysis_tasks.append((task, img_info))

                # 等待所有分析完成
                analysis_results = []
                for task, img_info in analysis_tasks:
                    try:
                        analysis = await task
                        # 添加图片元信息
                        analysis['image_info'] = {
                            'filename': img_info.get('filename', ''),
                            'original_url': img_info.get('original_url', ''),
                            'alt_text': img_info.get('alt_text', ''),
                            'title': img_info.get('title', '')
                        }
                        analysis_results.append(analysis)

                        if analysis.get('success'):
                            result['summary']['successful_analyses'] += 1

                    except Exception as e:
                        logger.error(f"图片分析失败: {e}")
                        analysis_results.append({
                            'success': False,
                            'error': str(e),
                            'image_info': img_info
                        })

                result['image_analyses'] = analysis_results
                result['summary']['images_analyzed'] = len(analysis_results)
                result['summary']['analysis_time'] = round(time.time() - start_time, 2)

        # 生成综合摘要
        result['comprehensive_summary'] = {
            'webpage_title': result['webpage_info']['title'],
            'content_length': result['webpage_info']['text_length'],
            'images_found': result['webpage_info']['total_images_found'],
            'images_analyzed': result['summary']['images_analyzed'],
            'successful_image_analyses': result['summary']['successful_analyses'],
            'has_images': len(images) > 0,
            'analysis_complete': True
        }

        logger.info(f"完整分析完成 - 文本: {result['webpage_info']['text_length']} 字符, 图片: {result['summary']['successful_analyses']}/{result['summary']['images_analyzed']}")
        return result

    except Exception as e:
        logger.error(f"完整网页分析失败: {e}")
        return {
            'success': False,
            'url': url,
            'error': str(e),
            'comprehensive_summary': {
                'analysis_complete': False,
                'error_occurred': True
            }
        }

@mcp.tool()
async def get_scraped_images() -> List[Dict[str, Any]]:
    """
    Get list of all scraped images in the output directory
    
    Returns:
        List of image file information
    """
    try:
        output_dir = Path(settings.output_dir)
        if not output_dir.exists():
            return []
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
        images = []
        
        for file_path in output_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                stat = file_path.stat()
                images.append({
                    'filename': file_path.name,
                    'path': str(file_path),
                    'size_bytes': stat.st_size,
                    'modified_time': stat.st_mtime,
                    'extension': file_path.suffix.lower()
                })
        
        # Sort by modification time (newest first)
        images.sort(key=lambda x: x['modified_time'], reverse=True)
        
        logger.info(f"Found {len(images)} scraped images")
        return images
        
    except Exception as e:
        logger.error(f"Error getting scraped images: {e}")
        return []

if __name__ == "__main__":
    logger.info("🚀 Starting MCP Web Scraper Service...")
    logger.info(f"📍 Server URL: http://{settings.mcp_host}:{settings.mcp_port}/sse")
    logger.info(f"📁 Output directory: {settings.output_dir}")
    logger.info(f"🤖 Doubao model: {settings.doubao_model_id}")

    # Ensure output directory exists
    from config import ensure_output_dir
    ensure_output_dir()
    logger.info("✅ Output directory ready")

    # Test Doubao API configuration (optional)
    try:
        DoubaoImageAnalyzer()
        logger.info("✅ Doubao API configuration loaded")
    except Exception as e:
        logger.warning(f"⚠️ Doubao API configuration issue: {e}")

    logger.info("🎉 Starting server - ready to accept connections!")

    # Run with SSE transport
    mcp.run(
        transport="sse",
        host=settings.mcp_host,
        port=settings.mcp_port
    )
