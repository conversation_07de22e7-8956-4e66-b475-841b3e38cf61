"""
使用uv启动MCP服务器的脚本
"""
import subprocess
import sys
import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def start_server():
    """启动MCP服务器"""
    try:
        logger.info("🚀 使用uv启动MCP服务器...")
        
        # 使用uv运行服务器
        cmd = ["uv", "run", "python", "main_clean.py"]
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        logger.info("✅ 服务器进程已启动")
        logger.info("📍 服务器地址: http://127.0.0.1:8000/sse")
        logger.info("🔍 实时日志输出:")
        logger.info("-" * 50)
        
        # 实时输出日志
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(line.rstrip())
                    
        except KeyboardInterrupt:
            logger.info("\n⏹️ 收到停止信号，正在关闭服务器...")
            process.terminate()
            process.wait()
            logger.info("✅ 服务器已停止")
            
    except FileNotFoundError:
        logger.error("❌ 找不到uv命令，请确保已安装uv")
        logger.info("安装uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 启动服务器失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_server()
