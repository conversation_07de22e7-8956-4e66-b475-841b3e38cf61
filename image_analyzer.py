"""
Image analysis using Doubao vision model
"""
import base64
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
import httpx
from PIL import Image
import io
import logging

from config import get_settings

logger = logging.getLogger(__name__)

class DoubaoImageAnalyzer:
    """Doubao vision model image analyzer"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = httpx.AsyncClient(
            base_url=self.settings.doubao_base_url,
            headers={
                "Authorization": f"Bearer {self.settings.doubao_api_key}",
                "Content-Type": "application/json"
            },
            timeout=60.0
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """Encode image file to base64 string"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding image {image_path}: {e}")
            raise
    
    def _encode_image_bytes_to_base64(self, image_bytes: bytes) -> str:
        """Encode image bytes to base64 string"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def _optimize_image(self, image_path: str, max_size: int = 1024) -> bytes:
        """Optimize image size for API"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Resize if too large
                if max(img.size) > max_size:
                    ratio = max_size / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # Save to bytes
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85, optimize=True)
                return buffer.getvalue()
        except Exception as e:
            logger.error(f"Error optimizing image {image_path}: {e}")
            # Fallback to original file
            with open(image_path, "rb") as f:
                return f.read()
    
    async def analyze_image(
        self, 
        image_path: str, 
        prompt: str = "请详细描述这张图片的内容，包括主要元素、颜色、布局和任何文字信息。",
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze a single image using Doubao vision model
        
        Args:
            image_path: Path to the image file
            prompt: Analysis prompt
            context: Additional context about the image
            
        Returns:
            Analysis result dictionary
        """
        try:
            # Optimize and encode image
            image_bytes = self._optimize_image(image_path)
            image_base64 = self._encode_image_bytes_to_base64(image_bytes)
            
            # Prepare the request
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"{prompt}\n\n{context if context else ''}"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            payload = {
                "model": self.settings.doubao_model_id,
                "messages": messages,
                "max_tokens": 2000,
                "temperature": 0.1
            }
            
            # Make API request
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                analysis = result["choices"][0]["message"]["content"]
                return {
                    "success": True,
                    "image_path": image_path,
                    "analysis": analysis,
                    "model": self.settings.doubao_model_id,
                    "prompt": prompt,
                    "context": context,
                    "usage": result.get("usage", {})
                }
            else:
                return {
                    "success": False,
                    "error": "No analysis returned from model",
                    "image_path": image_path
                }
                
        except Exception as e:
            logger.error(f"Error analyzing image {image_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "image_path": image_path
            }
    
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        prompt: str = "请详细描述这张图片的内容。",
        context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Analyze multiple images concurrently
        
        Args:
            image_paths: List of image file paths
            prompt: Analysis prompt
            context: Additional context
            
        Returns:
            List of analysis results
        """
        tasks = [
            self.analyze_image(path, prompt, context) 
            for path in image_paths
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "image_path": image_paths[i]
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def analyze_image_with_webpage_context(
        self, 
        image_path: str, 
        webpage_text: str, 
        webpage_url: str
    ) -> Dict[str, Any]:
        """
        Analyze image with webpage context
        
        Args:
            image_path: Path to the image file
            webpage_text: Text content from the webpage
            webpage_url: URL of the webpage
            
        Returns:
            Analysis result with context
        """
        context = f"""
这张图片来自网页: {webpage_url}

网页文本内容摘要:
{webpage_text[:1000]}...

请结合网页内容分析这张图片，说明图片与网页内容的关系，以及图片在网页中的作用和意义。
        """.strip()
        
        prompt = "请详细分析这张图片，并结合提供的网页上下文信息，说明图片的内容、作用和与网页的关系。"
        
        return await self.analyze_image(image_path, prompt, context)

if __name__ == "__main__":
    import sys
    from pathlib import Path

    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    async def test_single_image_analysis():
        """Test analyzing a single image"""
        print("\n=== Testing Single Image Analysis ===")

        # Find a test image
        scraped_dir = Path("scraped_data")
        if not scraped_dir.exists():
            print("❌ No scraped_data directory found. Please run the scraper first.")
            return

        image_files = list(scraped_dir.glob("*.jpg")) + list(scraped_dir.glob("*.png"))
        if not image_files:
            print("❌ No image files found in scraped_data directory.")
            return

        test_image = str(image_files[0])
        print(f"📸 Testing with image: {test_image}")

        async with DoubaoImageAnalyzer() as analyzer:
            result = await analyzer.analyze_image(
                image_path=test_image,
                prompt="请详细描述这张图片的内容，包括主要元素、颜色、布局和任何文字信息。"
            )

            print(f"✅ Analysis completed successfully: {result['success']}")
            if result['success']:
                print(f"📝 Analysis: {result['analysis'][:200]}...")
                print(f"🔧 Model used: {result['model']}")
                if 'usage' in result:
                    print(f"📊 Token usage: {result['usage']}")
            else:
                print(f"❌ Error: {result['error']}")

    async def test_multiple_images_analysis():
        """Test analyzing multiple images concurrently"""
        print("\n=== Testing Multiple Images Analysis ===")

        scraped_dir = Path("scraped_data")
        if not scraped_dir.exists():
            print("❌ No scraped_data directory found.")
            return

        image_files = list(scraped_dir.glob("*.jpg")) + list(scraped_dir.glob("*.png"))
        if len(image_files) < 2:
            print("❌ Need at least 2 images for this test.")
            return

        # Test with first 3 images (or all if less than 3)
        test_images = [str(img) for img in image_files[:3]]
        print(f"📸 Testing with {len(test_images)} images")

        async with DoubaoImageAnalyzer() as analyzer:
            results = await analyzer.analyze_multiple_images(
                image_paths=test_images,
                prompt="简要描述这张图片的主要内容。"
            )

            print(f"✅ Analyzed {len(results)} images")
            for i, result in enumerate(results):
                if result['success']:
                    print(f"  Image {i+1}: ✅ {result['analysis'][:100]}...")
                else:
                    print(f"  Image {i+1}: ❌ {result['error']}")

    async def test_webpage_context_analysis():
        """Test analyzing image with webpage context"""
        print("\n=== Testing Webpage Context Analysis ===")

        scraped_dir = Path("scraped_data")
        image_files = list(scraped_dir.glob("*.jpg")) + list(scraped_dir.glob("*.png"))
        if not image_files:
            print("❌ No image files found.")
            return

        test_image = str(image_files[0])

        # Mock webpage context
        mock_webpage_text = """
        这是一个电商网站的商品页面，展示了各种时尚商品。
        页面包含商品图片、价格信息、用户评价等内容。
        主要销售服装、配饰和生活用品。
        """

        mock_webpage_url = "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"

        async with DoubaoImageAnalyzer() as analyzer:
            result = await analyzer.analyze_image_with_webpage_context(
                image_path=test_image,
                webpage_text=mock_webpage_text,
                webpage_url=mock_webpage_url
            )

            print(f"✅ Context analysis completed: {result['success']}")
            if result['success']:
                print(f"📝 Analysis with context: {result['analysis'][:300]}...")
            else:
                print(f"❌ Error: {result['error']}")

    async def test_image_optimization():
        """Test image optimization functionality"""
        print("\n=== Testing Image Optimization ===")

        scraped_dir = Path("scraped_data")
        image_files = list(scraped_dir.glob("*.jpg")) + list(scraped_dir.glob("*.png"))
        if not image_files:
            print("❌ No image files found.")
            return

        test_image = str(image_files[0])

        analyzer = DoubaoImageAnalyzer()

        # Test optimization
        try:
            original_size = Path(test_image).stat().st_size
            optimized_bytes = analyzer._optimize_image(test_image, max_size=512)
            optimized_size = len(optimized_bytes)

            print(f"📸 Original image: {test_image}")
            print(f"📊 Original size: {original_size:,} bytes")
            print(f"📊 Optimized size: {optimized_size:,} bytes")
            print(f"📉 Compression ratio: {optimized_size/original_size:.2%}")

            # Test base64 encoding
            base64_str = analyzer._encode_image_bytes_to_base64(optimized_bytes)
            print(f"📝 Base64 length: {len(base64_str):,} characters")

        except Exception as e:
            print(f"❌ Optimization test failed: {e}")

    async def run_all_tests():
        """Run all tests"""
        print("🚀 Starting DoubaoImageAnalyzer Tests")
        print("=" * 50)

        try:
            # Check if we have the required environment variables
            from config import get_settings
            settings = get_settings()

            if not settings.doubao_api_key:
                print("❌ DOUBAO_API_KEY not found in environment variables.")
                print("Please set up your .env file with the required API key.")
                return

            print(f"🔧 Using model: {settings.doubao_model_id}")
            print(f"🌐 API endpoint: {settings.doubao_base_url}")

            # Run tests
            await test_image_optimization()
            await test_single_image_analysis()
            await test_multiple_images_analysis()
            await test_webpage_context_analysis()

            print("\n" + "=" * 50)
            print("✅ All tests completed!")

        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            import traceback
            traceback.print_exc()

    # Run the tests
    if len(sys.argv) > 1:
        # Allow running specific tests
        test_name = sys.argv[1]
        if test_name == "single":
            asyncio.run(test_single_image_analysis())
        elif test_name == "multiple":
            asyncio.run(test_multiple_images_analysis())
        elif test_name == "context":
            asyncio.run(test_webpage_context_analysis())
        elif test_name == "optimize":
            asyncio.run(test_image_optimization())
        else:
            print("Available tests: single, multiple, context, optimize")
    else:
        # Run all tests
        asyncio.run(run_all_tests())

