"""
Usage examples for MCP Web Scraper Service
Demonstrates various use cases and scenarios
"""
import asyncio
import json
import logging
from fastmcp import Client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def example_basic_scraping():
    """Example 1: Basic webpage scraping"""
    async with Client("http://localhost:8001/sse") as client:
        
        # Scrape a simple webpage
        result = await client.call_tool("scrape_webpage", {
            "url": "https://httpbin.org/html"
        })
        
        if result and result[0].text:
            data = json.loads(result[0].text)
            print(f"Scraped {data['text_length']} characters from {data['url']}")
            print(f"Title: {data['title']}")
            print(f"Images found: {data['image_count']}")

async def example_image_analysis():
    """Example 2: Image analysis (requires local image file)"""
    async with C<PERSON>("http://localhost:8001/sse") as client:
        
        # First, get list of scraped images
        result = await client.call_tool("get_scraped_images", {})
        
        if result and result[0].text:
            images = json.loads(result[0].text)
            
            if images:
                # Analyze the first image
                image_path = images[0]['path']
                
                analysis_result = await client.call_tool("analyze_image", {
                    "image_path": image_path,
                    "prompt": "请详细描述这张图片的内容，包括颜色、形状、文字等信息。",
                    "context": "这是从网页中提取的图片"
                })
                
                if analysis_result and analysis_result[0].text:
                    analysis = json.loads(analysis_result[0].text)
                    if analysis['success']:
                        print(f"Image analysis: {analysis['analysis']}")
                    else:
                        print(f"Analysis failed: {analysis['error']}")
            else:
                print("No images available for analysis")

async def example_comprehensive_analysis():
    """Example 3: Comprehensive webpage analysis with images"""
    async with Client("http://localhost:8001/sse") as client:
        
        # Analyze a webpage that might have images
        result = await client.call_tool("scrape_and_analyze_webpage", {
            "url": "https://example.com",
            "image_analysis_prompt": "分析这张图片在网页中的作用和意义",
            "analyze_all_images": True,
            "max_images": 3
        })
        
        if result and result[0].text:
            data = json.loads(result[0].text)
            
            if data['success']:
                print(f"Comprehensive analysis completed:")
                print(f"- Text extracted: {data['summary']['text_length']} characters")
                print(f"- Images found: {data['summary']['total_images_found']}")
                print(f"- Images analyzed: {data['summary']['images_analyzed']}")
                print(f"- Successful analyses: {data['summary']['successful_analyses']}")
                
                # Show image analysis results
                for i, analysis in enumerate(data['image_analyses']):
                    if analysis['success']:
                        print(f"Image {i+1} analysis: {analysis['analysis'][:100]}...")
            else:
                print(f"Analysis failed: {data['error']}")

async def example_batch_image_analysis():
    """Example 4: Batch image analysis"""
    async with Client("http://localhost:8001/sse") as client:
        
        # Get all scraped images
        result = await client.call_tool("get_scraped_images", {})
        
        if result and result[0].text:
            images = json.loads(result[0].text)
            
            if len(images) >= 2:
                # Analyze multiple images
                image_paths = [img['path'] for img in images[:3]]  # First 3 images
                
                batch_result = await client.call_tool("analyze_multiple_images", {
                    "image_paths": image_paths,
                    "prompt": "简要描述这张图片的主要内容",
                    "context": "批量图片分析"
                })
                
                if batch_result and batch_result[0].text:
                    analyses = json.loads(batch_result[0].text)
                    
                    for i, analysis in enumerate(analyses):
                        if analysis['success']:
                            print(f"Image {i+1}: {analysis['analysis'][:100]}...")
                        else:
                            print(f"Image {i+1} failed: {analysis['error']}")
            else:
                print("Need at least 2 images for batch analysis demo")

async def example_custom_prompts():
    """Example 5: Using custom prompts for different analysis types"""
    async with Client("http://localhost:8001/sse") as client:
        
        # Get scraped images
        result = await client.call_tool("get_scraped_images", {})
        
        if result and result[0].text:
            images = json.loads(result[0].text)
            
            if images:
                image_path = images[0]['path']
                
                # Different types of analysis
                prompts = [
                    "请识别图片中的所有文字内容",
                    "请分析图片的色彩搭配和视觉效果",
                    "请描述图片中的人物、物体和场景",
                    "请评估这张图片的商业价值和用途"
                ]
                
                for i, prompt in enumerate(prompts):
                    result = await client.call_tool("analyze_image", {
                        "image_path": image_path,
                        "prompt": prompt
                    })
                    
                    if result and result[0].text:
                        analysis = json.loads(result[0].text)
                        if analysis['success']:
                            print(f"Analysis {i+1}: {analysis['analysis'][:150]}...")

async def run_all_examples():
    """Run all examples"""
    try:
        logger.info("🚀 Running MCP Web Scraper Usage Examples")
        
        logger.info("\n📝 Example 1: Basic Webpage Scraping")
        await example_basic_scraping()
        
        logger.info("\n🖼️ Example 2: Image Analysis")
        await example_image_analysis()
        
        logger.info("\n🔍 Example 3: Comprehensive Analysis")
        await example_comprehensive_analysis()
        
        logger.info("\n📊 Example 4: Batch Image Analysis")
        await example_batch_image_analysis()
        
        logger.info("\n🎨 Example 5: Custom Prompts")
        await example_custom_prompts()
        
        logger.info("\n✅ All examples completed!")
        
    except Exception as e:
        logger.error(f"❌ Examples failed: {e}")

if __name__ == "__main__":
    asyncio.run(run_all_examples())
