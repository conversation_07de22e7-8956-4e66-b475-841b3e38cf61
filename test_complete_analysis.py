"""
测试完整网页分析功能 - 一次请求获取文本和图片分析
"""
import asyncio
import json
import logging
from fastmcp import Client

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_complete_analysis():
    """测试完整网页分析功能"""
    
    try:
        logger.info("🚀 开始测试完整网页分析功能")
        
        async with Client("http://localhost:8001/sse") as client:
            
            # 测试URL列表
            test_urls = [
                "https://httpbin.org/html",  # 简单HTML页面
                "https://example.com",       # 基础示例页面
                # 可以添加更多有图片的网站进行测试
            ]
            
            for url in test_urls:
                logger.info(f"\n📄 分析网页: {url}")
                
                try:
                    # 调用完整分析工具
                    result = await client.call_tool("analyze_webpage_complete", {
                        "url": url,
                        "max_images": 3,
                        "image_prompt": "请详细描述这张图片，包括内容、颜色、布局，以及它在网页中的作用。"
                    })
                    
                    if result and result[0].text:
                        data = json.loads(result[0].text)
                        
                        if data['success']:
                            # 显示网页信息
                            webpage_info = data['webpage_info']
                            logger.info(f"✅ 网页分析成功:")
                            logger.info(f"   标题: {webpage_info['title']}")
                            logger.info(f"   文本长度: {webpage_info['text_length']} 字符")
                            logger.info(f"   发现图片: {webpage_info['total_images_found']} 张")
                            
                            # 显示文本内容预览
                            content_preview = webpage_info['text_content'][:200]
                            logger.info(f"   内容预览: {content_preview}...")
                            
                            # 显示图片分析结果
                            image_analyses = data['image_analyses']
                            if image_analyses:
                                logger.info(f"\n🖼️  图片分析结果 ({len(image_analyses)} 张):")
                                
                                for i, analysis in enumerate(image_analyses):
                                    if analysis['success']:
                                        img_info = analysis['image_info']
                                        logger.info(f"   图片 {i+1}:")
                                        logger.info(f"     文件名: {img_info['filename']}")
                                        logger.info(f"     Alt文本: {img_info['alt_text']}")
                                        logger.info(f"     分析结果: {analysis['analysis'][:150]}...")
                                    else:
                                        logger.warning(f"   图片 {i+1} 分析失败: {analysis['error']}")
                            else:
                                logger.info("   该网页没有图片需要分析")
                            
                            # 显示综合摘要
                            summary = data['comprehensive_summary']
                            logger.info(f"\n📊 综合摘要:")
                            logger.info(f"   分析完成: {summary['analysis_complete']}")
                            logger.info(f"   包含图片: {summary['has_images']}")
                            logger.info(f"   成功分析图片: {summary['successful_image_analyses']}/{summary['images_analyzed']}")
                            
                        else:
                            logger.error(f"❌ 分析失败: {data['error']}")
                    
                except Exception as e:
                    logger.error(f"❌ 请求失败: {e}")
                
                # 添加延迟避免请求过快
                await asyncio.sleep(2)
            
            logger.info("\n✅ 测试完成!")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        logger.info("请确保服务器正在运行: python main.py")

async def demo_usage():
    """演示如何使用完整分析功能"""
    
    logger.info("\n📖 使用示例:")
    logger.info("""
# Python 客户端使用示例
from fastmcp import Client
import asyncio

async def analyze_webpage():
    async with Client("http://localhost:8001/sse") as client:
        result = await client.call_tool("analyze_webpage_complete", {
            "url": "https://example.com",
            "max_images": 5,
            "image_prompt": "请分析这张图片的内容和在网页中的作用"
        })
        
        if result and result[0].text:
            data = json.loads(result[0].text)
            
            # 获取网页文本信息
            webpage_info = data['webpage_info']
            print(f"网页标题: {webpage_info['title']}")
            print(f"文本内容: {webpage_info['text_content']}")
            
            # 获取图片分析结果
            for analysis in data['image_analyses']:
                if analysis['success']:
                    print(f"图片分析: {analysis['analysis']}")

asyncio.run(analyze_webpage())
    """)

if __name__ == "__main__":
    asyncio.run(test_complete_analysis())
    asyncio.run(demo_usage())
