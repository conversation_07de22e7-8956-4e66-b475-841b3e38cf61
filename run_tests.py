#!/usr/bin/env python3
"""
Test runner script for image_analyzer.py tests
"""
import subprocess
import sys
import os
from pathlib import Path

def install_test_dependencies():
    """Install test dependencies using uv"""
    print("📦 Installing test dependencies...")
    try:
        result = subprocess.run([
            "uv", "pip", "install", "-r", "test_requirements.txt"
        ], check=True, capture_output=True, text=True)
        print("✅ Test dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install test dependencies: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ uv not found. Please install uv or use pip directly:")
        print("pip install -r test_requirements.txt")
        return False

def run_tests():
    """Run the test suite"""
    print("\n🧪 Running image_analyzer tests...")
    
    # Test command with verbose output and coverage
    test_cmd = [
        "python", "-m", "pytest", 
        "test_image_analyzer.py",
        "-v",                    # verbose output
        "--tb=short",           # short traceback format
        "--cov=image_analyzer", # coverage for image_analyzer module
        "--cov-report=term-missing",  # show missing lines
        "--asyncio-mode=auto"   # auto detect asyncio tests
    ]
    
    try:
        result = subprocess.run(test_cmd, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def run_specific_test(test_name):
    """Run a specific test"""
    print(f"\n🎯 Running specific test: {test_name}")
    
    test_cmd = [
        "python", "-m", "pytest", 
        f"test_image_analyzer.py::{test_name}",
        "-v",
        "--tb=short",
        "--asyncio-mode=auto"
    ]
    
    try:
        result = subprocess.run(test_cmd, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

def main():
    """Main test runner"""
    print("🚀 Image Analyzer Test Runner")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("image_analyzer.py").exists():
        print("❌ image_analyzer.py not found. Please run from the project root.")
        sys.exit(1)
    
    # Install dependencies if needed
    if not install_test_dependencies():
        print("⚠️  Continuing without installing dependencies...")
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        success = run_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
