"""
Edge cases and performance tests for image_analyzer.py
"""
import pytest
import asyncio
import tempfile
import os
import time
from unittest.mock import Mock, AsyncMock, patch
from PIL import Image
import io
import httpx

from image_analyzer import DoubaoImageAnalyzer


class TestDoubaoImageAnalyzerEdgeCases:
    """Edge case tests for DoubaoImageAnalyzer"""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock()
        settings.doubao_base_url = "https://test-api.com"
        settings.doubao_api_key = "test-key"
        settings.doubao_model_id = "test-model"
        return settings
    
    @pytest.fixture
    def analyzer(self, mock_settings):
        """Create analyzer instance with mocked settings"""
        with patch('image_analyzer.get_settings', return_value=mock_settings):
            with patch('httpx.AsyncClient') as mock_client:
                analyzer = DoubaoImageAnalyzer()
                analyzer.client = AsyncMock()
                return analyzer

    def test_encode_empty_file(self, analyzer):
        """Test encoding empty file"""
        # Create empty file
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            result = analyzer._encode_image_to_base64(tmp_path)
            # Empty file should still produce valid base64
            assert isinstance(result, str)
            assert result == ""  # base64 of empty bytes
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_encode_corrupted_image(self, analyzer):
        """Test encoding corrupted image file"""
        # Create file with invalid image data
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tmp_file.write(b"This is not an image file")
            tmp_path = tmp_file.name
        
        try:
            # Should still encode the bytes, even if not valid image
            result = analyzer._encode_image_to_base64(tmp_path)
            assert isinstance(result, str)
            assert len(result) > 0
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_optimize_corrupted_image_fallback(self, analyzer):
        """Test optimization fallback for corrupted image"""
        # Create file with invalid image data
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            invalid_data = b"This is not an image file"
            tmp_file.write(invalid_data)
            tmp_path = tmp_file.name
        
        try:
            # Should fallback to original file content
            result = analyzer._optimize_image(tmp_path)
            assert result == invalid_data
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    def test_optimize_very_small_image(self, analyzer):
        """Test optimization of very small image"""
        # Create 1x1 pixel image
        tiny_img = Image.new('RGB', (1, 1), color='red')
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tiny_img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            result = analyzer._optimize_image(tmp_path, max_size=100)
            
            # Should handle tiny images without error
            assert isinstance(result, bytes)
            assert len(result) > 0
            
            # Verify it's still a valid image
            optimized_img = Image.open(io.BytesIO(result))
            assert optimized_img.size == (1, 1)
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_analyze_image_timeout(self, analyzer):
        """Test image analysis with timeout"""
        # Create test image
        img = Image.new('RGB', (50, 50), color='blue')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            # Mock timeout error
            analyzer.client.post = AsyncMock(side_effect=httpx.TimeoutException("Request timeout"))
            
            result = await analyzer.analyze_image(tmp_path)
            
            assert result["success"] is False
            assert "timeout" in result["error"].lower()
            assert result["image_path"] == tmp_path
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_analyze_image_network_error(self, analyzer):
        """Test image analysis with network error"""
        # Create test image
        img = Image.new('RGB', (50, 50), color='green')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            # Mock network error
            analyzer.client.post = AsyncMock(side_effect=httpx.NetworkError("Network unreachable"))
            
            result = await analyzer.analyze_image(tmp_path)
            
            assert result["success"] is False
            assert "network" in result["error"].lower()
            assert result["image_path"] == tmp_path
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_analyze_image_malformed_response(self, analyzer):
        """Test image analysis with malformed API response"""
        # Create test image
        img = Image.new('RGB', (50, 50), color='yellow')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            # Mock malformed response
            mock_response = Mock()
            mock_response.json.side_effect = ValueError("Invalid JSON")
            mock_response.raise_for_status = Mock()
            
            analyzer.client.post = AsyncMock(return_value=mock_response)
            
            result = await analyzer.analyze_image(tmp_path)
            
            assert result["success"] is False
            assert "error" in result
            assert result["image_path"] == tmp_path
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_analyze_multiple_images_empty_list(self, analyzer):
        """Test analyzing empty list of images"""
        results = await analyzer.analyze_multiple_images([])
        
        assert isinstance(results, list)
        assert len(results) == 0

    @pytest.mark.asyncio
    async def test_analyze_image_with_very_long_prompt(self, analyzer):
        """Test image analysis with very long prompt"""
        # Create test image
        img = Image.new('RGB', (50, 50), color='purple')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            # Create very long prompt
            long_prompt = "请分析这张图片" * 1000  # Very long prompt
            
            # Mock successful response
            mock_response = Mock()
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Analysis result"}}],
                "usage": {"total_tokens": 100}
            }
            mock_response.raise_for_status = Mock()
            analyzer.client.post = AsyncMock(return_value=mock_response)
            
            result = await analyzer.analyze_image(tmp_path, prompt=long_prompt)
            
            assert result["success"] is True
            assert result["prompt"] == long_prompt
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_analyze_image_with_unicode_context(self, analyzer):
        """Test image analysis with unicode characters in context"""
        # Create test image
        img = Image.new('RGB', (50, 50), color='orange')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            # Unicode context with various characters
            unicode_context = "测试中文 🎨 العربية русский 日本語 emoji 🚀"
            
            # Mock successful response
            mock_response = Mock()
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Unicode analysis result"}}],
                "usage": {"total_tokens": 100}
            }
            mock_response.raise_for_status = Mock()
            analyzer.client.post = AsyncMock(return_value=mock_response)
            
            result = await analyzer.analyze_image(tmp_path, context=unicode_context)
            
            assert result["success"] is True
            assert result["context"] == unicode_context
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)


class TestDoubaoImageAnalyzerPerformance:
    """Performance tests for DoubaoImageAnalyzer"""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock()
        settings.doubao_base_url = "https://test-api.com"
        settings.doubao_api_key = "test-key"
        settings.doubao_model_id = "test-model"
        return settings
    
    @pytest.fixture
    def analyzer(self, mock_settings):
        """Create analyzer instance with mocked settings"""
        with patch('image_analyzer.get_settings', return_value=mock_settings):
            with patch('httpx.AsyncClient') as mock_client:
                analyzer = DoubaoImageAnalyzer()
                analyzer.client = AsyncMock()
                return analyzer

    @pytest.mark.slow
    def test_optimize_large_image_performance(self, analyzer):
        """Test performance of optimizing large images"""
        # Create large test image
        large_img = Image.new('RGB', (4000, 4000), color='blue')
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            large_img.save(tmp_file, format='JPEG', quality=95)
            tmp_path = tmp_file.name
        
        try:
            start_time = time.time()
            result = analyzer._optimize_image(tmp_path, max_size=1024)
            end_time = time.time()
            
            # Should complete within reasonable time (less than 5 seconds)
            assert (end_time - start_time) < 5.0
            
            # Should produce smaller result
            with open(tmp_path, 'rb') as f:
                original_size = len(f.read())
            assert len(result) < original_size
            
            # Verify optimized image dimensions
            optimized_img = Image.open(io.BytesIO(result))
            assert max(optimized_img.size) <= 1024
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_concurrent_analysis_performance(self, analyzer):
        """Test performance of concurrent image analysis"""
        # Create multiple test images
        test_images = []
        for i in range(5):
            img = Image.new('RGB', (100, 100), color=['red', 'green', 'blue', 'yellow', 'purple'][i])
            with tempfile.NamedTemporaryFile(suffix=f'_perf_{i}.jpg', delete=False) as tmp_file:
                img.save(tmp_file, format='JPEG')
                test_images.append(tmp_file.name)
        
        try:
            # Mock fast responses
            mock_response = Mock()
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Fast analysis"}}],
                "usage": {"total_tokens": 50}
            }
            mock_response.raise_for_status = Mock()
            analyzer.client.post = AsyncMock(return_value=mock_response)
            
            start_time = time.time()
            results = await analyzer.analyze_multiple_images(test_images)
            end_time = time.time()
            
            # Concurrent processing should be faster than sequential
            # (This is a rough estimate, actual timing depends on system)
            assert (end_time - start_time) < 2.0
            assert len(results) == 5
            assert all(result["success"] for result in results)
        finally:
            # Cleanup
            for img_path in test_images:
                if os.path.exists(img_path):
                    os.unlink(img_path)


if __name__ == "__main__":
    # Run edge case tests
    pytest.main([__file__, "-v", "--tb=short", "-m", "not slow"])
