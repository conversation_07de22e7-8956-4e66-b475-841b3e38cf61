"""
Demonstration script for MCP Web Scraper Service
Shows how to use the various tools available
"""
import asyncio
import json
import logging
from fastmcp import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def wait_for_server(url: str, max_retries: int = 5, delay: float = 2.0):
    """Wait for server to be ready"""
    for attempt in range(max_retries):
        try:
            async with Client(url) as client:
                await client.list_tools()
                logger.info(f"✅ Server is ready after {attempt + 1} attempts")
                return True
        except Exception as e:
            if attempt < max_retries - 1:
                logger.info(f"⏳ Server not ready (attempt {attempt + 1}/{max_retries}): {e}")
                await asyncio.sleep(delay)
            else:
                logger.error(f"❌ Server failed to start after {max_retries} attempts: {e}")
                return False
    return False

async def demo_web_scraper():
    """Demonstrate the web scraper capabilities"""

    server_url = "http://localhost:8000/sse"

    try:
        logger.info("🚀 Starting MCP Web Scraper Demo")

        # Wait for server to be ready
        logger.info("⏳ Waiting for server to be ready...")
        if not await wait_for_server(server_url):
            logger.error("❌ Server is not responding. Make sure it's running with: python main.py")
            return

        logger.info(f"🔗 Connecting to server at {server_url}")

        async with Client(server_url) as client:
            
            # Demo 1: List available tools
            logger.info("\n📋 Demo 1: Available Tools")
            tools = await client.list_tools()
            for tool in tools:
                logger.info(f"  - {tool.name}: {tool.description}")
            
            # Demo 2: Simple webpage scraping
            logger.info("\n🌐 Demo 2: Simple Webpage Scraping")
            test_url = "https://httpbin.org/html"
            logger.info(f"Scraping: {test_url}")
            
            result = await client.call_tool("scrape_webpage", {"url": test_url})
            if result and result[0].text:
                data = json.loads(result[0].text)
                if data.get('success'):
                    logger.info(f"✅ Success! Extracted {data['text_length']} characters")
                    logger.info(f"   Title: {data['title']}")
                    logger.info(f"   Images found: {data['image_count']}")
                    logger.info(f"   Content preview: {data['main_content'][:200]}...")
                else:
                    logger.error(f"❌ Scraping failed: {data.get('error')}")
            
            # Demo 3: Get scraped images
            logger.info("\n🖼️  Demo 3: List Scraped Images")
            result = await client.call_tool("get_scraped_images", {})
            if result and result[0].text:
                images = json.loads(result[0].text)
                logger.info(f"Found {len(images)} scraped images in output directory")
                for img in images[:3]:  # Show first 3
                    logger.info(f"  - {img['filename']} ({img['size_bytes']} bytes)")
            
            # Demo 4: Try scraping a page with images (example)
            logger.info("\n🎨 Demo 4: Scraping Page with Images")
            image_url = "https://httpbin.org/image/png"  # This will redirect to an image
            logger.info(f"Trying to scrape: {image_url}")
            
            try:
                result = await client.call_tool("scrape_webpage", {"url": image_url})
                if result and result[0].text:
                    data = json.loads(result[0].text)
                    if data.get('success'):
                        logger.info(f"✅ Success! Found {data['image_count']} images")
                    else:
                        logger.info(f"ℹ️  Note: {data.get('error', 'No images found')}")
            except Exception as e:
                logger.info(f"ℹ️  Expected: {e}")
            
            # Demo 5: Comprehensive webpage analysis (if we had images)
            logger.info("\n🔍 Demo 5: Comprehensive Analysis")
            logger.info("This would analyze both text and images with Doubao vision model")
            logger.info("Example usage:")
            logger.info("  await client.call_tool('scrape_and_analyze_webpage', {")
            logger.info("    'url': 'https://example.com',")
            logger.info("    'max_images': 5")
            logger.info("  })")
            
            logger.info("\n✨ Demo completed successfully!")
            logger.info("\n📝 Available Tools Summary:")
            logger.info("  1. scrape_webpage - Extract text and images from any URL")
            logger.info("  2. analyze_image - Analyze images using Doubao vision model")
            logger.info("  3. analyze_multiple_images - Batch image analysis")
            logger.info("  4. scrape_and_analyze_webpage - Complete webpage analysis")
            logger.info("  5. get_scraped_images - List all downloaded images")
            
            logger.info("\n🔧 Configuration:")
            logger.info(f"  - Server: http://localhost:8001/sse")
            logger.info(f"  - Output directory: scraped_data/")
            logger.info(f"  - Doubao model: doubao-1-5-vision-pro-32k-250115")
            
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        logger.info("Make sure the server is running with: python main.py")

if __name__ == "__main__":
    asyncio.run(demo_web_scraper())
