"""
Simple test to verify MCP server basic functionality
"""
import asyncio
import logging
from fastmcp import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def simple_test():
    """Simple test to check if server is responding"""
    
    server_url = "http://localhost:8000/sse"
    
    try:
        logger.info("🔍 Testing basic server connectivity...")
        
        # Try to connect with a short timeout
        async with Client(server_url) as client:
            logger.info("✅ Connected successfully!")
            
            # Try to list tools
            tools = await client.list_tools()
            logger.info(f"📋 Found {len(tools)} tools:")
            for tool in tools:
                logger.info(f"  - {tool.name}: {tool.description}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(simple_test())
    if success:
        logger.info("🎉 Server is working correctly!")
    else:
        logger.error("💥 Server test failed!")
