"""
Test script for the MCP Web Scraper Service
"""
import asyncio
import logging
from fastmcp import Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def wait_for_server(url: str, max_retries: int = 10, delay: float = 2.0):
    """Wait for server to be ready"""
    for attempt in range(max_retries):
        try:
            async with Client(url) as client:
                # Try to list tools as a health check
                await client.list_tools()
                logger.info(f"Server is ready after {attempt + 1} attempts")
                return True
        except Exception as e:
            if attempt < max_retries - 1:
                logger.info(f"Server not ready (attempt {attempt + 1}/{max_retries}): {e}")
                await asyncio.sleep(delay)
            else:
                logger.error(f"Server failed to start after {max_retries} attempts: {e}")
                return False
    return False

async def test_server():
    """Test the MCP server functionality"""

    server_url = "http://localhost:8000/sse"

    try:
        # Wait for server to be ready
        logger.info("Waiting for MCP server to be ready...")
        if not await wait_for_server(server_url):
            logger.error("Server is not responding. Make sure it's running with: python main.py")
            return

        # Connect to the server
        logger.info("Connecting to MCP server...")
        async with Client(server_url) as client:
            
            # Test 1: List available tools
            logger.info("Testing: List tools")
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")
            
            # Test 2: Get scraped images (should return empty list initially)
            logger.info("Testing: Get scraped images")
            result = await client.call_tool("get_scraped_images", {})
            logger.info(f"Scraped images: {len(result[0].text) if result else 0}")
            
            # Test 3: Try to scrape a simple webpage
            logger.info("Testing: Scrape webpage")
            test_url = "https://httpbin.org/html"
            
            try:
                result = await client.call_tool("scrape_webpage", {"url": test_url})
                if result:
                    logger.info(f"Scraping result: {result[0].text[:200]}...")
                else:
                    logger.warning("No result from scraping")
            except Exception as e:
                logger.error(f"Scraping test failed: {e}")
            
            logger.info("All tests completed!")
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
        logger.info("Make sure the server is running with: python main.py")

if __name__ == "__main__":
    asyncio.run(test_server())
