"""
使用uv测试MCP服务器
"""
import asyncio
import logging
import json
import time
from fastmcp import Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def wait_for_server(url: str, max_retries: int = 10, delay: float = 2.0):
    """等待服务器启动"""
    logger.info(f"⏳ 等待服务器启动: {url}")
    
    for attempt in range(max_retries):
        try:
            async with Client(url) as client:
                await client.list_tools()
                logger.info(f"✅ 服务器已就绪 (尝试 {attempt + 1}/{max_retries})")
                return True
        except Exception as e:
            if attempt < max_retries - 1:
                logger.info(f"⏳ 服务器未就绪 (尝试 {attempt + 1}/{max_retries}): {e}")
                await asyncio.sleep(delay)
            else:
                logger.error(f"❌ 服务器启动失败，已尝试 {max_retries} 次: {e}")
                return False
    return False

async def test_server():
    """测试服务器功能"""
    
    server_url = "http://localhost:8001/sse"
    
    try:
        # 等待服务器就绪
        if not await wait_for_server(server_url):
            logger.error("❌ 服务器未响应，请确保服务器正在运行")
            logger.info("启动服务器: python start_server.py")
            return
        
        logger.info("🔗 连接到MCP服务器...")
        
        async with Client(server_url) as client:
            logger.info("✅ 连接成功!")
            
            # 测试1: 列出可用工具
            logger.info("📋 获取可用工具...")
            tools = await client.list_tools()
            logger.info(f"可用工具: {[tool.name for tool in tools]}")
            
            # 测试2: 测试主要接口
            test_url = "https://httpbin.org/html"
            logger.info(f"🌐 测试抓取和分析: {test_url}")
            
            start_time = time.time()
            result = await client.call_tool("scrape_and_analyze", {
                "url": test_url,
                "max_images": 3
            })
            end_time = time.time()
            
            if result:
                try:
                    data = json.loads(result[0].text)
                    
                    logger.info("🎉 测试成功!")
                    logger.info(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
                    logger.info("📊 结果摘要:")
                    logger.info(f"   - 成功: {data.get('success', False)}")
                    logger.info(f"   - 网页标题: {data.get('title', 'N/A')}")
                    
                    text_content = data.get('text_content', '')
                    text_length = len(text_content) if isinstance(text_content, str) else 0
                    logger.info(f"   - 文本长度: {text_length} 字符")
                    logger.info(f"   - 图片总数: {data.get('total_images', 0)}")
                    logger.info(f"   - 分析图片: {data.get('analyzed_images', 0)}")
                    
                    # 显示图片分析结果
                    image_analyses = data.get('image_analyses', [])
                    if image_analyses:
                        logger.info("📸 图片分析结果:")
                        for i, img_analysis in enumerate(image_analyses[:2]):  # 只显示前2个
                            analysis = img_analysis.get('analysis', {})
                            if analysis.get('success'):
                                analysis_text = analysis.get('analysis', 'N/A')
                                logger.info(f"   图片{i+1}: {analysis_text[:100]}...")
                            else:
                                logger.info(f"   图片{i+1}: 分析失败 - {analysis.get('error', 'N/A')}")
                    
                    # 显示部分文本内容
                    if text_content:
                        logger.info("📝 网页内容预览:")
                        preview = text_content[:200] + "..." if len(text_content) > 200 else text_content
                        logger.info(f"   {preview}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON解析失败: {e}")
                    logger.info(f"原始响应: {result[0].text[:500]}...")
                    
            else:
                logger.warning("❌ 没有返回结果")
            
            logger.info("🎉 所有测试完成!")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("🧪 开始测试MCP服务器...")
    asyncio.run(test_server())
