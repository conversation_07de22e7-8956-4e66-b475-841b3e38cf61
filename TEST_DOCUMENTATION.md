# Image Analyzer Test Documentation

This document describes the comprehensive test suite for the `image_analyzer.py` module.

## Test Files Overview

### 1. `test_image_analyzer.py`
Main test file containing core functionality tests:

- **Unit Tests for Core Methods:**
  - `_encode_image_to_base64()` - Image file to base64 encoding
  - `_encode_image_bytes_to_base64()` - Bytes to base64 encoding
  - `_optimize_image()` - Image optimization and resizing
  - `analyze_image()` - Single image analysis
  - `analyze_multiple_images()` - Concurrent image analysis
  - `analyze_image_with_webpage_context()` - Contextual analysis

- **Integration Tests:**
  - End-to-end workflow testing
  - Async context manager behavior
  - API request payload validation

### 2. `test_image_analyzer_edge_cases.py`
Edge cases and performance tests:

- **Edge Cases:**
  - Empty files
  - Corrupted images
  - Network errors
  - Malformed API responses
  - Unicode handling
  - Very long prompts

- **Performance Tests:**
  - Large image optimization
  - Concurrent analysis performance

## Test Configuration

### Dependencies
```bash
# Install test dependencies
uv pip install -r test_requirements.txt
```

Required packages:
- `pytest>=7.0.0` - Test framework
- `pytest-asyncio>=0.21.0` - Async test support
- `pytest-mock>=3.10.0` - Mocking utilities
- `pytest-cov>=4.0.0` - Coverage reporting

### Configuration Files
- `pytest.ini` - Pytest configuration
- `test_requirements.txt` - Test dependencies

## Running Tests

### Quick Start
```bash
# Run all tests
python run_tests.py

# Run specific test
python run_tests.py TestDoubaoImageAnalyzer::test_encode_image_to_base64_success

# Run with coverage
pytest test_image_analyzer.py --cov=image_analyzer --cov-report=html
```

### Test Categories

#### Unit Tests
```bash
pytest test_image_analyzer.py::TestDoubaoImageAnalyzer -v
```

#### Integration Tests
```bash
pytest test_image_analyzer.py::TestDoubaoImageAnalyzerIntegration -v
```

#### Edge Cases
```bash
pytest test_image_analyzer_edge_cases.py::TestDoubaoImageAnalyzerEdgeCases -v
```

#### Performance Tests (Slow)
```bash
pytest test_image_analyzer_edge_cases.py::TestDoubaoImageAnalyzerPerformance -v -m slow
```

## Test Coverage

The test suite covers:

### ✅ Covered Functionality
- Image encoding (base64)
- Image optimization and resizing
- API request handling
- Error handling and exceptions
- Async operations
- Context manager behavior
- Multiple image processing
- Unicode and special characters
- Network error scenarios

### 🎯 Test Scenarios
- **Happy Path:** Normal operation with valid inputs
- **Error Handling:** Invalid files, network errors, API failures
- **Edge Cases:** Empty files, corrupted data, extreme inputs
- **Performance:** Large images, concurrent operations
- **Integration:** End-to-end workflows

## Mock Strategy

The tests use comprehensive mocking to avoid external dependencies:

### Mocked Components
- **HTTP Client:** `httpx.AsyncClient` for API calls
- **Settings:** Configuration values
- **File System:** Temporary files for testing
- **API Responses:** Doubao vision model responses

### Mock Examples
```python
# Mock successful API response
mock_response = Mock()
mock_response.json.return_value = {
    "choices": [{"message": {"content": "Analysis result"}}],
    "usage": {"total_tokens": 100}
}
analyzer.client.post = AsyncMock(return_value=mock_response)
```

## Test Data

### Image Generation
Tests create temporary images using PIL:
```python
# Create test image
img = Image.new('RGB', (100, 100), color='red')
with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
    img.save(tmp_file, format='JPEG')
    test_image_path = tmp_file.name
```

### Cleanup
All temporary files are properly cleaned up using try/finally blocks.

## Assertions and Validation

### Response Structure Validation
```python
assert result["success"] is True
assert result["image_path"] == expected_path
assert "analysis" in result
assert "usage" in result
```

### API Call Validation
```python
# Verify correct API endpoint and payload
analyzer.client.post.assert_called_once_with("/chat/completions", json=pytest.mock.ANY)
```

## Continuous Integration

### GitHub Actions (Example)
```yaml
- name: Run Tests
  run: |
    uv pip install -r test_requirements.txt
    pytest test_image_analyzer.py test_image_analyzer_edge_cases.py --cov=image_analyzer
```

## Troubleshooting

### Common Issues

1. **Import Errors:**
   ```bash
   # Ensure all dependencies are installed
   uv pip install -r test_requirements.txt
   ```

2. **Async Test Issues:**
   ```bash
   # Use pytest-asyncio
   pytest --asyncio-mode=auto
   ```

3. **Mock Issues:**
   ```python
   # Ensure proper patching
   with patch('image_analyzer.get_settings', return_value=mock_settings):
   ```

### Debug Mode
```bash
# Run with verbose output and no capture
pytest test_image_analyzer.py -v -s --tb=long
```

## Best Practices

1. **Isolation:** Each test is independent
2. **Cleanup:** Temporary files are always cleaned up
3. **Mocking:** External dependencies are mocked
4. **Coverage:** Aim for >90% code coverage
5. **Performance:** Mark slow tests appropriately
6. **Documentation:** Clear test names and docstrings

## Future Enhancements

- Add property-based testing with Hypothesis
- Add load testing for concurrent scenarios
- Add integration tests with real API (optional)
- Add visual regression testing for image optimization
