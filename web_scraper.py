"""
Web scraping using <PERSON><PERSON> headless browser
"""
import asyncio
import os
import re
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from urllib.parse import urljoin, urlparse
import logging

from playwright.async_api import async_playwright, <PERSON>, Browser
from bs4 import BeautifulSoup
import requests

from config import get_settings, ensure_output_dir

logger = logging.getLogger(__name__)

class WebScraper:
    """Headless browser web scraper using <PERSON><PERSON>"""
    
    def __init__(self):
        self.settings = get_settings()
        self.output_dir = ensure_output_dir()
        self.browser: Optional[Browser] = None
        self.playwright = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self):
        """Start the browser"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.settings.headless,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            )
            logger.info("Browser started successfully")
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise
    
    async def close(self):
        """Close the browser"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        # Remove common unwanted characters
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        return text
    
    def _generate_filename(self, url: str, extension: str = "") -> str:
        """Generate a safe filename from URL"""
        # Create hash of URL for uniqueness
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        
        # Extract domain and path
        parsed = urlparse(url)
        domain = parsed.netloc.replace('www.', '')
        path = parsed.path.strip('/').replace('/', '_')
        
        # Create filename
        if path:
            filename = f"{domain}_{path}_{url_hash}"
        else:
            filename = f"{domain}_{url_hash}"
        
        # Clean filename
        filename = re.sub(r'[^\w\-_.]', '_', filename)
        filename = re.sub(r'_+', '_', filename)
        
        if extension:
            filename += f".{extension}"
        
        return filename
    
    async def _download_image(self, image_url: str, base_url: str) -> Optional[str]:
        """Download an image and return the local file path"""
        try:
            # Resolve relative URLs
            full_url = urljoin(base_url, image_url)
            
            # Generate filename
            parsed_url = urlparse(full_url)
            extension = Path(parsed_url.path).suffix.lower()
            if not extension or extension not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                extension = '.jpg'
            
            filename = self._generate_filename(full_url, extension.lstrip('.'))
            file_path = os.path.join(self.output_dir, filename)
            
            # Skip if already downloaded
            if os.path.exists(file_path):
                return file_path
            
            # Download image
            headers = {
                'User-Agent': self.settings.user_agent,
                'Referer': base_url
            }
            
            response = requests.get(full_url, headers=headers, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                logger.warning(f"URL {full_url} is not an image: {content_type}")
                return None
            
            # Save image
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Downloaded image: {filename}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to download image {image_url}: {e}")
            return None
    
    async def _extract_images(self, page: Page, base_url: str) -> List[Dict[str, Any]]:
        """Extract and download images from the page"""
        try:
            # Get all image elements
            image_elements = await page.query_selector_all('img')
            
            images_info = []
            downloaded_count = 0
            
            for img in image_elements:
                if downloaded_count >= self.settings.max_images_per_page:
                    break
                
                try:
                    # Get image attributes
                    src = await img.get_attribute('src')
                    alt = await img.get_attribute('alt') or ""
                    title = await img.get_attribute('title') or ""
                    
                    if not src:
                        continue
                    
                    # Skip data URLs and very small images
                    if src.startswith('data:') or 'pixel' in src.lower():
                        continue
                    
                    # Download image
                    local_path = await self._download_image(src, base_url)
                    
                    if local_path:
                        images_info.append({
                            'original_url': urljoin(base_url, src),
                            'local_path': local_path,
                            'alt_text': self._clean_text(alt),
                            'title': self._clean_text(title),
                            'filename': os.path.basename(local_path)
                        })
                        downloaded_count += 1
                
                except Exception as e:
                    logger.error(f"Error processing image element: {e}")
                    continue
            
            logger.info(f"Downloaded {len(images_info)} images from {base_url}")
            return images_info
            
        except Exception as e:
            logger.error(f"Error extracting images: {e}")
            return []
    
    async def scrape_webpage(self, url: str) -> Dict[str, Any]:
        """
        Scrape a webpage and extract text content and images
        
        Args:
            url: URL to scrape
            
        Returns:
            Dictionary containing scraped data
        """
        if not self.browser:
            raise RuntimeError("Browser not started. Use async context manager or call start() first.")
        
        try:
            # Create new page
            page = await self.browser.new_page()
            
            # Set user agent
            await page.set_extra_http_headers({
                'User-Agent': self.settings.user_agent
            })
            
            # Navigate to page
            logger.info(f"Navigating to: {url}")
            response = await page.goto(
                url, 
                wait_until='domcontentloaded',
                timeout=self.settings.max_wait_time
            )
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for page to load
            await page.wait_for_load_state('networkidle', timeout=self.settings.max_wait_time)
            
            # Get page title
            title = await page.title()
            
            # Get page content
            content = await page.content()
            
            # Parse with BeautifulSoup for text extraction
            soup = BeautifulSoup(content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # Extract text content
            text_content = soup.get_text()
            cleaned_text = self._clean_text(text_content)
            
            # Extract main content (try to find article, main, or content divs)
            main_content = ""
            for selector in ['article', 'main', '[role="main"]', '.content', '.post', '.article']:
                main_element = soup.select_one(selector)
                if main_element:
                    main_content = self._clean_text(main_element.get_text())
                    break
            
            if not main_content:
                main_content = cleaned_text
            
            # Extract meta information
            meta_description = ""
            meta_keywords = ""
            
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                meta_description = meta_desc.get('content', '')
            
            meta_kw = soup.find('meta', attrs={'name': 'keywords'})
            if meta_kw:
                meta_keywords = meta_kw.get('content', '')
            
            # Extract images
            images = await self._extract_images(page, url)
            
            # Close page
            await page.close()
            
            result = {
                'success': True,
                'url': url,
                'title': self._clean_text(title),
                'meta_description': self._clean_text(meta_description),
                'meta_keywords': self._clean_text(meta_keywords),
                'text_content': cleaned_text,
                'main_content': main_content,
                'images': images,
                'image_count': len(images),
                'text_length': len(cleaned_text),
                'timestamp': asyncio.get_event_loop().time()
            }
            
            logger.info(f"Successfully scraped {url}: {len(cleaned_text)} chars, {len(images)} images")
            return result
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return {
                'success': False,
                'url': url,
                'error': str(e),
                'timestamp': asyncio.get_event_loop().time()
            }
