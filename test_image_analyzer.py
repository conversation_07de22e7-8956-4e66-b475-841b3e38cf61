"""
Comprehensive test cases for image_analyzer.py
"""
import pytest
import asyncio
import base64
import json
import tempfile
import os
from unittest.mock import Mock, AsyncMock, patch, mock_open
from pathlib import Path
from PIL import Image
import io
import httpx

from image_analyzer import DoubaoImageAnalyzer


class TestDoubaoImageAnalyzer:
    """Test cases for DoubaoImageAnalyzer class"""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock()
        settings.doubao_base_url = "https://test-api.com"
        settings.doubao_api_key = "test-key"
        settings.doubao_model_id = "test-model"
        return settings
    
    @pytest.fixture
    def analyzer(self, mock_settings):
        """Create analyzer instance with mocked settings"""
        with patch('image_analyzer.get_settings', return_value=mock_settings):
            with patch('httpx.AsyncClient') as mock_client:
                analyzer = DoubaoImageAnalyzer()
                analyzer.client = AsyncMock()
                return analyzer
    
    @pytest.fixture
    def sample_image_path(self):
        """Create a temporary test image file"""
        # Create a simple test image
        img = Image.new('RGB', (100, 100), color='red')
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        yield tmp_path
        
        # Cleanup
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
    
    @pytest.fixture
    def sample_image_bytes(self):
        """Create sample image bytes"""
        img = Image.new('RGB', (50, 50), color='blue')
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG')
        return buffer.getvalue()

    def test_encode_image_to_base64_success(self, analyzer, sample_image_path):
        """Test successful image encoding to base64"""
        result = analyzer._encode_image_to_base64(sample_image_path)
        
        assert isinstance(result, str)
        assert len(result) > 0
        # Verify it's valid base64
        decoded = base64.b64decode(result)
        assert len(decoded) > 0
    
    def test_encode_image_to_base64_file_not_found(self, analyzer):
        """Test encoding non-existent file raises exception"""
        with pytest.raises(FileNotFoundError):
            analyzer._encode_image_to_base64("non_existent_file.jpg")
    
    def test_encode_image_bytes_to_base64(self, analyzer, sample_image_bytes):
        """Test encoding image bytes to base64"""
        result = analyzer._encode_image_bytes_to_base64(sample_image_bytes)
        
        assert isinstance(result, str)
        assert len(result) > 0
        # Verify it's valid base64
        decoded = base64.b64decode(result)
        assert decoded == sample_image_bytes
    
    def test_optimize_image_success(self, analyzer, sample_image_path):
        """Test successful image optimization"""
        result = analyzer._optimize_image(sample_image_path, max_size=50)
        
        assert isinstance(result, bytes)
        assert len(result) > 0
        
        # Verify the optimized image is smaller or equal to original
        with open(sample_image_path, 'rb') as f:
            original_size = len(f.read())
        assert len(result) <= original_size
    
    def test_optimize_image_large_image(self, analyzer):
        """Test optimization of large image"""
        # Create a large test image
        large_img = Image.new('RGB', (2000, 2000), color='green')
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            large_img.save(tmp_file, format='JPEG')
            tmp_path = tmp_file.name
        
        try:
            result = analyzer._optimize_image(tmp_path, max_size=500)
            
            # Verify the result is optimized
            optimized_img = Image.open(io.BytesIO(result))
            assert max(optimized_img.size) <= 500
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_optimize_image_rgba_conversion(self, analyzer):
        """Test RGBA image conversion to RGB"""
        # Create RGBA image
        rgba_img = Image.new('RGBA', (100, 100), color=(255, 0, 0, 128))
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            rgba_img.save(tmp_file, format='PNG')
            tmp_path = tmp_file.name
        
        try:
            result = analyzer._optimize_image(tmp_path)
            
            # Verify the result is in JPEG format (RGB)
            optimized_img = Image.open(io.BytesIO(result))
            assert optimized_img.mode == 'RGB'
        finally:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    def test_optimize_image_fallback_on_error(self, analyzer, sample_image_path):
        """Test fallback to original file when optimization fails"""
        with patch('image_analyzer.Image.open', side_effect=Exception("PIL error")):
            result = analyzer._optimize_image(sample_image_path)
            
            # Should fallback to original file content
            with open(sample_image_path, 'rb') as f:
                original_content = f.read()
            assert result == original_content

    @pytest.mark.asyncio
    async def test_analyze_image_success(self, analyzer, sample_image_path):
        """Test successful image analysis"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": "This is a test image analysis result."
                }
            }],
            "usage": {"total_tokens": 100}
        }
        mock_response.raise_for_status = Mock()
        
        analyzer.client.post = AsyncMock(return_value=mock_response)
        
        result = await analyzer.analyze_image(
            sample_image_path, 
            prompt="Test prompt",
            context="Test context"
        )
        
        assert result["success"] is True
        assert result["image_path"] == sample_image_path
        assert result["analysis"] == "This is a test image analysis result."
        assert result["prompt"] == "Test prompt"
        assert result["context"] == "Test context"
        assert "usage" in result
    
    @pytest.mark.asyncio
    async def test_analyze_image_api_error(self, analyzer, sample_image_path):
        """Test image analysis with API error"""
        # Mock API error
        analyzer.client.post = AsyncMock(side_effect=httpx.HTTPStatusError(
            "API Error", request=Mock(), response=Mock()
        ))
        
        result = await analyzer.analyze_image(sample_image_path)
        
        assert result["success"] is False
        assert "error" in result
        assert result["image_path"] == sample_image_path
    
    @pytest.mark.asyncio
    async def test_analyze_image_no_choices(self, analyzer, sample_image_path):
        """Test image analysis with no choices in response"""
        # Mock response with no choices
        mock_response = Mock()
        mock_response.json.return_value = {"choices": []}
        mock_response.raise_for_status = Mock()
        
        analyzer.client.post = AsyncMock(return_value=mock_response)
        
        result = await analyzer.analyze_image(sample_image_path)
        
        assert result["success"] is False
        assert result["error"] == "No analysis returned from model"
        assert result["image_path"] == sample_image_path

    @pytest.mark.asyncio
    async def test_analyze_multiple_images_success(self, analyzer):
        """Test successful analysis of multiple images"""
        # Create multiple test images
        test_images = []
        for i in range(3):
            img = Image.new('RGB', (50, 50), color=['red', 'green', 'blue'][i])
            with tempfile.NamedTemporaryFile(suffix=f'_test_{i}.jpg', delete=False) as tmp_file:
                img.save(tmp_file, format='JPEG')
                test_images.append(tmp_file.name)
        
        try:
            # Mock successful responses
            mock_response = Mock()
            mock_response.json.return_value = {
                "choices": [{
                    "message": {"content": f"Analysis result"}
                }],
                "usage": {"total_tokens": 50}
            }
            mock_response.raise_for_status = Mock()
            analyzer.client.post = AsyncMock(return_value=mock_response)
            
            results = await analyzer.analyze_multiple_images(test_images)
            
            assert len(results) == 3
            for i, result in enumerate(results):
                assert result["success"] is True
                assert result["image_path"] == test_images[i]
                assert "analysis" in result
        
        finally:
            # Cleanup
            for img_path in test_images:
                if os.path.exists(img_path):
                    os.unlink(img_path)

    @pytest.mark.asyncio
    async def test_analyze_multiple_images_with_exceptions(self, analyzer):
        """Test multiple image analysis with some failures"""
        # Create test images
        test_images = []
        for i in range(2):
            img = Image.new('RGB', (50, 50), color=['red', 'green'][i])
            with tempfile.NamedTemporaryFile(suffix=f'_test_{i}.jpg', delete=False) as tmp_file:
                img.save(tmp_file, format='JPEG')
                test_images.append(tmp_file.name)

        # Add non-existent file
        test_images.append("non_existent_file.jpg")

        try:
            # Mock mixed responses - some success, some failure
            def mock_analyze_image(path, prompt, context):
                if "non_existent" in path:
                    raise FileNotFoundError("File not found")
                return {
                    "success": True,
                    "image_path": path,
                    "analysis": f"Test analysis for {prompt} with context: {context}"
                }

            analyzer.analyze_image = AsyncMock(side_effect=mock_analyze_image)

            results = await analyzer.analyze_multiple_images(test_images)

            assert len(results) == 3
            # First two should succeed
            assert results[0]["success"] is True
            assert results[1]["success"] is True
            # Third should fail
            assert results[2]["success"] is False
            assert "error" in results[2]

        finally:
            # Cleanup existing files
            for img_path in test_images[:2]:
                if os.path.exists(img_path):
                    os.unlink(img_path)

    @pytest.mark.asyncio
    async def test_analyze_image_with_webpage_context(self, analyzer, sample_image_path):
        """Test image analysis with webpage context"""
        webpage_text = "This is sample webpage content about products and services."
        webpage_url = "https://example.com/products"

        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": "Analysis with webpage context: The image relates to the webpage content."
                }
            }],
            "usage": {"total_tokens": 150}
        }
        mock_response.raise_for_status = Mock()
        analyzer.client.post = AsyncMock(return_value=mock_response)

        result = await analyzer.analyze_image_with_webpage_context(
            sample_image_path, webpage_text, webpage_url
        )

        assert result["success"] is True
        assert result["image_path"] == sample_image_path
        assert "Analysis with webpage context" in result["analysis"]
        assert webpage_url in result["context"]
        assert webpage_text[:1000] in result["context"]

    @pytest.mark.asyncio
    async def test_async_context_manager(self, mock_settings):
        """Test async context manager behavior"""
        with patch('image_analyzer.get_settings', return_value=mock_settings):
            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_client_class.return_value = mock_client

                async with DoubaoImageAnalyzer() as analyzer:
                    assert analyzer is not None
                    assert hasattr(analyzer, 'client')

                # Verify client was closed
                mock_client.aclose.assert_called_once()

    def test_init_with_settings(self, mock_settings):
        """Test analyzer initialization with settings"""
        with patch('image_analyzer.get_settings', return_value=mock_settings):
            with patch('httpx.AsyncClient') as mock_client_class:
                analyzer = DoubaoImageAnalyzer()

                # Verify analyzer was created and client was initialized
                assert analyzer is not None
                assert hasattr(analyzer, 'settings')

                # Verify client was created with correct parameters
                mock_client_class.assert_called_once_with(
                    base_url=mock_settings.doubao_base_url,
                    headers={
                        "Authorization": f"Bearer {mock_settings.doubao_api_key}",
                        "Content-Type": "application/json"
                    },
                    timeout=60.0
                )

    @pytest.mark.asyncio
    async def test_analyze_image_request_payload(self, analyzer, sample_image_path):
        """Test that analyze_image sends correct request payload"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [{"message": {"content": "Test result"}}],
            "usage": {"total_tokens": 100}
        }
        mock_response.raise_for_status = Mock()
        analyzer.client.post = AsyncMock(return_value=mock_response)

        prompt = "Custom test prompt"
        context = "Custom test context"

        await analyzer.analyze_image(sample_image_path, prompt, context)

        # Verify the API call was made with correct parameters
        analyzer.client.post.assert_called_once_with("/chat/completions", json=pytest.mock.ANY)

        # Get the actual payload that was sent
        call_args = analyzer.client.post.call_args
        payload = call_args[1]['json']

        # Verify payload structure
        assert payload["model"] == analyzer.settings.doubao_model_id
        assert payload["max_tokens"] == 2000
        assert payload["temperature"] == 0.1
        assert len(payload["messages"]) == 1

        message = payload["messages"][0]
        assert message["role"] == "user"
        assert len(message["content"]) == 2

        # Verify text content
        text_content = message["content"][0]
        assert text_content["type"] == "text"
        assert prompt in text_content["text"]
        assert context in text_content["text"]

        # Verify image content
        image_content = message["content"][1]
        assert image_content["type"] == "image_url"
        assert "data:image/jpeg;base64," in image_content["image_url"]["url"]


# Integration tests
class TestDoubaoImageAnalyzerIntegration:
    """Integration tests for DoubaoImageAnalyzer"""

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow with mocked external dependencies"""
        # Create test image
        img = Image.new('RGB', (100, 100), color='purple')
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            img.save(tmp_file, format='JPEG')
            test_image_path = tmp_file.name

        try:
            # Mock settings
            mock_settings = Mock()
            mock_settings.doubao_base_url = "https://test-api.com"
            mock_settings.doubao_api_key = "test-key"
            mock_settings.doubao_model_id = "test-model"

            with patch('image_analyzer.get_settings', return_value=mock_settings):
                with patch('httpx.AsyncClient') as mock_client_class:
                    # Mock successful API response
                    mock_response = Mock()
                    mock_response.json.return_value = {
                        "choices": [{
                            "message": {
                                "content": "This is a purple square image for testing purposes."
                            }
                        }],
                        "usage": {"total_tokens": 75}
                    }
                    mock_response.raise_for_status = Mock()

                    mock_client = AsyncMock()
                    mock_client.post = AsyncMock(return_value=mock_response)
                    mock_client_class.return_value = mock_client

                    # Test the complete workflow
                    async with DoubaoImageAnalyzer() as analyzer:
                        result = await analyzer.analyze_image(
                            test_image_path,
                            prompt="Describe this test image",
                            context="This is a unit test"
                        )

                        # Verify results
                        assert result["success"] is True
                        assert result["image_path"] == test_image_path
                        assert "purple square image" in result["analysis"]
                        assert result["prompt"] == "Describe this test image"
                        assert result["context"] == "This is a unit test"
                        assert result["usage"]["total_tokens"] == 75

        finally:
            # Cleanup
            if os.path.exists(test_image_path):
                os.unlink(test_image_path)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
