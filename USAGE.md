# MCP Web Scraper 使用说明

## 简化版本说明

现在只有一个主要工具接口：`scrape_and_analyze`

- **功能**: 一次性抓取网页并分析图片
- **输入**: URL和最大图片分析数量
- **输出**: 完整的网页文本和图片分析结果

## 使用uv启动服务器

### 1. 启动服务器

```bash
# 方法1: 使用启动脚本 (推荐)
python start_server.py

# 方法2: 直接使用uv
uv run python main_clean.py
```

### 2. 测试服务器

在另一个终端中运行：

```bash
# 使用uv运行测试
uv run python test_with_uv.py
```

## 日志功能

服务器会输出详细的日志信息：

- 🚀 启动信息
- 📄 网页抓取进度
- 🖼️ 图片发现和分析
- ✅ 成功/失败状态
- 📊 统计信息

日志同时输出到：
- 控制台 (实时显示)
- 文件 `mcp_server.log` (持久保存)

## 工具接口

### scrape_and_analyze

```python
{
    "url": "https://example.com",
    "max_images": 5  # 可选，默认5张
}
```

**返回结果**:
```json
{
    "success": true,
    "url": "https://example.com",
    "title": "网页标题",
    "text_content": "完整文本内容",
    "main_content": "主要内容",
    "meta_description": "页面描述",
    "total_images": 10,
    "analyzed_images": 5,
    "image_analyses": [
        {
            "image_index": 1,
            "filename": "image1.jpg",
            "url": "https://example.com/image1.jpg",
            "local_path": "/path/to/image1.jpg",
            "analysis": {
                "success": true,
                "analysis": "图片分析结果..."
            }
        }
    ],
    "timestamp": 1234567890
}
```

## 环境配置

确保 `.env` 文件配置正确：

```env
# Doubao API配置
DOUBAO_API_KEY=your_api_key_here
DOUBAO_MODEL_ID=doubao-1-5-vision-pro-32k-250115

# 服务器配置
MCP_HOST=127.0.0.1
MCP_PORT=8000

# 输出目录
OUTPUT_DIR=scraped_data
```

## 故障排除

1. **服务器启动失败**
   - 检查uv是否安装: `uv --version`
   - 检查依赖是否安装: `uv sync`

2. **连接失败**
   - 确保服务器正在运行
   - 检查端口8000是否被占用

3. **图片分析失败**
   - 检查Doubao API密钥是否正确
   - 查看日志文件获取详细错误信息

## 文件说明

- `main_clean.py` - 简化版服务器 (只有一个工具)
- `start_server.py` - 启动脚本
- `test_with_uv.py` - 测试脚本
- `mcp_server.log` - 日志文件
