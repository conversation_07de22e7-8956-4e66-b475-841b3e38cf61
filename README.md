# MCP Web Scraper Service with Vision Analysis

A comprehensive Model Context Protocol (MCP) server that provides web scraping capabilities with image analysis using the Doubao vision model. The server uses a headless browser for web scraping and integrates with Doubao's vision API for intelligent image analysis.

## Features

- **SSE Interface**: Server-Sent Events transport for real-time communication
- **Web Scraping**: Extract text content and images from web pages using Playwright
- **Image Analysis**: Analyze images using Doubao vision model with contextual understanding
- **Batch Processing**: Analyze multiple images concurrently
- **Contextual Analysis**: Combine webpage content with image analysis for better insights

## Tools Available

### 1. `scrape_webpage(url: str)`
Scrapes a webpage and extracts text content and images.

**Parameters:**
- `url`: The URL to scrape

**Returns:**
- Webpage text content, metadata, and downloaded images

### 2. `analyze_image(image_path: str, prompt: str, context: str)`
Analyzes a single image using Doubao vision model.

**Parameters:**
- `image_path`: Path to the image file
- `prompt`: Analysis prompt (default in Chinese)
- `context`: Additional context for analysis

**Returns:**
- Detailed image analysis results

### 3. `analyze_multiple_images(image_paths: List[str], prompt: str, context: str)`
Analyzes multiple images concurrently.

**Parameters:**
- `image_paths`: List of image file paths
- `prompt`: Analysis prompt for all images
- `context`: Additional context for analysis

**Returns:**
- List of analysis results for each image

### 4. `scrape_and_analyze_webpage(url: str, image_analysis_prompt: str, analyze_all_images: bool, max_images: int)`
Comprehensive webpage analysis - scrapes content and analyzes images with context.

**Parameters:**
- `url`: The URL to scrape
- `image_analysis_prompt`: Prompt for image analysis
- `analyze_all_images`: Whether to analyze all images
- `max_images`: Maximum number of images to analyze

**Returns:**
- Complete webpage analysis with image insights

### 5. `get_scraped_images()`
Lists all scraped images in the output directory.

**Returns:**
- List of image file information

## Installation

1. **Install dependencies:**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

2. **Configure environment:**
```bash
# Copy and edit the environment file
cp .env.example .env
# Edit .env with your Doubao API key
```

3. **Set up Doubao API:**
- Get your API key from [Volcano Engine](https://console.volcengine.com/)
- Update `DOUBAO_API_KEY` in `.env` file

## Usage

### Start the Server

```bash
# Run with Python
python main.py

# Or use FastMCP CLI
fastmcp run main.py
```

The server will start on `http://127.0.0.1:8000` with SSE transport.

### Example Usage

```python
from fastmcp import Client
import asyncio

async def main():
    # Connect to the server
    async with Client("http://localhost:8000/sse") as client:
        
        # Scrape a webpage
        result = await client.call_tool("scrape_webpage", {
            "url": "https://example.com"
        })
        print(f"Scraped {result[0].text}")
        
        # Analyze webpage with images
        analysis = await client.call_tool("scrape_and_analyze_webpage", {
            "url": "https://example.com",
            "max_images": 5
        })
        print(f"Analysis: {analysis[0].text}")

asyncio.run(main())
```

## Configuration

Edit `.env` file to customize:

```env
# Doubao API Configuration
DOUBAO_API_KEY=your_api_key_here
DOUBAO_MODEL_ID=doubao-1-5-vision-pro-32k-250115

# Server Configuration
MCP_HOST=127.0.0.1
MCP_PORT=8000

# Scraping Configuration
MAX_WAIT_TIME=30000
MAX_IMAGES_PER_PAGE=10
OUTPUT_DIR=scraped_data
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│   FastMCP SSE    │───▶│   Web Scraper   │
│                 │    │     Server       │    │   (Playwright)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Doubao Vision   │
                       │     Model       │
                       └─────────────────┘
```

## Dependencies

- **FastMCP**: MCP server framework with SSE support
- **Playwright**: Headless browser automation
- **BeautifulSoup4**: HTML parsing
- **Pillow**: Image processing
- **httpx**: HTTP client for Doubao API
- **pydantic**: Configuration management

## Error Handling

The service includes comprehensive error handling:
- Network timeouts and retries
- Image download failures
- API rate limiting
- Invalid URLs and missing files

## Logging

Logs are written to console with timestamps. Configure logging level in `main.py`:

```python
logging.basicConfig(level=logging.INFO)
```

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
- Check the logs for error details
- Verify your Doubao API key is valid
- Ensure Playwright browsers are installed
- Check network connectivity
