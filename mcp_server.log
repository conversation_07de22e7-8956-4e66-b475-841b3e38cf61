2025-06-07 16:36:33,350 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 16:36:33,356 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 16:36:33,356 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 16:36:33,356 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 16:36:33,357 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 16:36:33,357 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 16:36:33,357 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 16:36:33,357 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 16:36:33,358 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 16:36:33,358 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8000/sse
2025-06-07 16:36:33,358 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 16:36:33,358 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 16:36:33,359 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 16:36:34,836 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 16:36:34,837 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 16:36:34,844 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 16:36:34,845 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 16:36:34,848 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8000/sse
2025-06-07 16:37:15,938 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 16:37:15,939 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 16:37:15,941 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 16:37:15,941 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8001/sse
2025-06-07 16:37:15,941 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 16:37:15,941 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 16:37:15,941 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 16:37:17,448 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 16:37:17,448 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 16:37:17,453 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 16:37:17,455 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 16:37:17,456 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8001/sse
2025-06-07 16:37:44,974 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:37:44,974 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 4019b3d9-b666-4462-a244-a2f77344b821
2025-06-07 16:37:44,974 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:37:44,975 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:37:44,977 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:37:44,977 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=4019b3d9b6664462a244a2f77344b821
2025-06-07 16:37:44,977 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=4019b3d9b6664462a244a2f77344b821\r\n\r\n'
2025-06-07 16:37:44,983 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:44,983 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4019b3d9-b666-4462-a244-a2f77344b821
2025-06-07 16:37:44,983 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"mcp","version":"0.1.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 16:37:44,984 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)
2025-06-07 16:37:44,984 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1236F00>))
2025-06-07 16:37:44,986 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 16:37:44,986 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 16:37:44,992 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:44,992 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4019b3d9-b666-4462-a244-a2f77344b821
2025-06-07 16:37:44,992 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 16:37:44,992 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:37:44,992 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1237680>))
2025-06-07 16:37:44,994 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:37:44,998 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:44,998 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4019b3d9-b666-4462-a244-a2f77344b821
2025-06-07 16:37:44,998 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 16:37:44,999 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 16:37:44,999 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1237C80>))
2025-06-07 16:37:45,000 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000002DDF1237AA0>
2025-06-07 16:37:45,001 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 16:37:45,001 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 16:37:45,001 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:37:45,001 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 16:37:45,002 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 16:37:45,004 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 16:37:45,004 - root - DEBUG - [sse.py:159] - Client session disconnected 4019b3d9-b666-4462-a244-a2f77344b821
2025-06-07 16:37:46,453 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:37:46,453 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:37:46,453 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:37:46,453 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:37:46,457 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:37:46,457 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=d6e2122e89b74c198d7088572fafa97c
2025-06-07 16:37:46,457 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=d6e2122e89b74c198d7088572fafa97c\r\n\r\n'
2025-06-07 16:37:46,463 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:46,463 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:37:46,463 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"mcp","version":"0.1.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 16:37:46,463 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)
2025-06-07 16:37:46,464 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1274D70>))
2025-06-07 16:37:46,466 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 16:37:46,466 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 16:37:46,476 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:46,479 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:37:46,479 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 16:37:46,479 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:37:46,479 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1275610>))
2025-06-07 16:37:46,481 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:37:46,486 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:46,487 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:37:46,487 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 16:37:46,487 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 16:37:46,487 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF1275BE0>))
2025-06-07 16:37:46,489 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000002DDF1275970>
2025-06-07 16:37:46,489 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 16:37:46,490 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 16:37:46,490 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:37:46,490 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 16:37:46,491 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 16:37:46,497 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:37:46,497 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:37:46,497 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://httpbin.org/html","max_images":3},"_meta":{"progressToken":2}},"jsonrpc":"2.0","id":2}'
2025-06-07 16:37:46,498 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://httpbin.org/html', 'max_images': 3}, '_meta': {'progressToken': 2}}, jsonrpc='2.0', id=2)
2025-06-07 16:37:46,498 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://httpbin.org/html', 'max_images': 3}, '_meta': {'progressToken': 2}}, jsonrpc='2.0', id=2)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002DDF12762A0>))
2025-06-07 16:37:46,501 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000002DDF10260C0>
2025-06-07 16:37:46,501 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 16:37:46,501 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 16:37:46,501 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://httpbin.org/html
2025-06-07 16:37:46,501 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=3
2025-06-07 16:37:46,501 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 16:37:50,998 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 16:37:51,263 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://httpbin.org/html
2025-06-07 16:37:53,336 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 0 images from https://httpbin.org/html
2025-06-07 16:37:53,353 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://httpbin.org/html: 3594 chars, 0 images
2025-06-07 16:37:53,416 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 16:37:53,416 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 
2025-06-07 16:37:53,416 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 3594 字符
2025-06-07 16:37:53,416 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 0 张
2025-06-07 16:37:53,416 - __main__ - INFO - [main_clean.py:107] - ℹ️ 网页中没有发现图片
2025-06-07 16:37:53,416 - __main__ - INFO - [main_clean.py:124] - 🎉 分析完成 - URL: https://httpbin.org/html
2025-06-07 16:37:53,417 - __main__ - INFO - [main_clean.py:125] - 📝 文本: 3594 字符
2025-06-07 16:37:53,417 - __main__ - INFO - [main_clean.py:126] - 🖼️ 图片: 0/0 张成功分析
2025-06-07 16:37:53,419 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:37:53,419 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=2, result={'content': [{'type': 'text', 'text': '{\n  "success": true,\n  "url": "https://httpbin.org/html",\n  "title": "",\n  "text_content": "Herman Melville - Moby-Dick Availing himself of the mild, summer-cool weather that now reigned in these latitudes, and in preparation for the peculiarly active pursuits shortly to be anticipated, Perth, the begrimed, blistered old blacksmith, had not removed his portable forge to the hold again, after concluding his contributory work for Ahab\'s leg, but still retained it on deck, fast lashed to ringbolts by the foremast; being now almost incessantly invoked by the headsmen, and harpooneers, and bowsmen to do some little job for them; altering, or repairing, or new shaping their various weapons and boat furniture. Often he would be surrounded by an eager circle, all waiting to be served; holding boat-spades, pike-heads, harpoons, and lances, and jealously watching his every sooty movement, as he toiled. Nevertheless, this old man\'s was a patient hammer wielded by a patient arm. No murmur, no impatience, no petulance did come from him. Silent, slow, and solemn; bowing over still further his chronically broken back, he toiled away, as if toil were life itself, and the heavy beating of his hammer the heavy beating of his heart. And so it was.—Most miserable! A peculiar walk in this old man, a certain slight but painful appearing yawing in his gait, had at an early period of the voyage excited the curiosity of the mariners. And to the importunity of their persisted questionings he had finally given in; and so it came to pass that every one now knew the shameful story of his wretched fate. Belated, and not innocently, one bitter winter\'s midnight, on the road running between two country towns, the blacksmith half-stupidly felt the deadly numbness stealing over him, and sought refuge in a leaning, dilapidated barn. The issue was, the loss of the extremities of both feet. Out of this revelation, part by part, at last came out the four acts of the gladness, and the one long, and as yet uncatastrophied fifth act of the grief of his life\'s drama. He was an old man, who, at the age of nearly sixty, had postponedly encountered that thing in sorrow\'s technicals called ruin. He had been an artisan of famed excellence, and with plenty to do; owned a house and garden; embraced a youthful, daughter-like, loving wife, and three blithe, ruddy children; every Sunday went to a cheerful-looking church, planted in a grove. But one night, under cover of darkness, and further concealed in a most cunning disguisement, a desperate burglar slid into his happy home, and robbed them all of everything. And darker yet to tell, the blacksmith himself did ignorantly conduct this burglar into his family\'s heart. It was the Bottle Conjuror! Upon the opening of that fatal cork, forth flew the fiend, and shrivelled up his home. Now, for prudent, most wise, and economic reasons, the blacksmith\'s shop was in the basement of his dwelling, but with a separate entrance to it; so that always had the young and loving healthy wife listened with no unhappy nervousness, but with vigorous pleasure, to the stout ringing of her young-armed old husband\'s hammer; whose reverberations, muffled by passing through the floors and walls, came up to her, not unsweetly, in her nursery; and so, to stout Labor\'s iron lullaby, the blacksmith\'s infants were rocked to slumber. Oh, woe on woe! Oh, Death, why canst thou not sometimes be timely? Hadst thou taken this old blacksmith to thyself ere his full ruin came upon him, then had the young widow had a delicious grief, and her orphans a truly venerable, legendary sire to dream of in their after years; and all of them a care-killing competency.",\n  "main_content": "Herman Melville - Moby-Dick Availing himself of the mild, summer-cool weather that now reigned in these latitudes, and in preparation for the peculiarly active pursuits shortly to be anticipated, Perth, the begrimed, blistered old blacksmith, had not removed his portable forge to the hold again, after concluding his contributory work for Ahab\'s leg, but still retained it on deck, fast lashed to ringbolts by the foremast; being now almost incessantly invoked by the headsmen, and harpooneers, and bowsmen to do some little job for them; altering, or repairing, or new shaping their various weapons and boat furniture. Often he would be surrounded by an eager circle, all waiting to be served; holding boat-spades, pike-heads, harpoons, and lances, and jealously watching his every sooty movement, as he toiled. Nevertheless, this old man\'s was a patient hammer wielded by a patient arm. No murmur, no impatience, no petulance did come from him. Silent, slow, and solemn; bowing over still further his chronically broken back, he toiled away, as if toil were life itself, and the heavy beating of his hammer the heavy beating of his heart. And so it was.—Most miserable! A peculiar walk in this old man, a certain slight but painful appearing yawing in his gait, had at an early period of the voyage excited the curiosity of the mariners. And to the importunity of their persisted questionings he had finally given in; and so it came to pass that every one now knew the shameful story of his wretched fate. Belated, and not innocently, one bitter winter\'s midnight, on the road running between two country towns, the blacksmith half-stupidly felt the deadly numbness stealing over him, and sought refuge in a leaning, dilapidated barn. The issue was, the loss of the extremities of both feet. Out of this revelation, part by part, at last came out the four acts of the gladness, and the one long, and as yet uncatastrophied fifth act of the grief of his life\'s drama. He was an old man, who, at the age of nearly sixty, had postponedly encountered that thing in sorrow\'s technicals called ruin. He had been an artisan of famed excellence, and with plenty to do; owned a house and garden; embraced a youthful, daughter-like, loving wife, and three blithe, ruddy children; every Sunday went to a cheerful-looking church, planted in a grove. But one night, under cover of darkness, and further concealed in a most cunning disguisement, a desperate burglar slid into his happy home, and robbed them all of everything. And darker yet to tell, the blacksmith himself did ignorantly conduct this burglar into his family\'s heart. It was the Bottle Conjuror! Upon the opening of that fatal cork, forth flew the fiend, and shrivelled up his home. Now, for prudent, most wise, and economic reasons, the blacksmith\'s shop was in the basement of his dwelling, but with a separate entrance to it; so that always had the young and loving healthy wife listened with no unhappy nervousness, but with vigorous pleasure, to the stout ringing of her young-armed old husband\'s hammer; whose reverberations, muffled by passing through the floors and walls, came up to her, not unsweetly, in her nursery; and so, to stout Labor\'s iron lullaby, the blacksmith\'s infants were rocked to slumber. Oh, woe on woe! Oh, Death, why canst thou not sometimes be timely? Hadst thou taken this old blacksmith to thyself ere his full ruin came upon him, then had the young widow had a delicious grief, and her orphans a truly venerable, legendary sire to dream of in their after years; and all of them a care-killing competency.",\n  "meta_description": "",\n  "total_images": 0,\n  "analyzed_images": 0,\n  "image_analyses": [],\n  "timestamp": 178554.203\n}'}], 'isError': False})), metadata=None)
2025-06-07 16:37:53,420 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":2,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": true,\\n  \\"url\\": \\"https://httpbin.org/html\\",\\n  \\"title\\": \\"\\",\\n  \\"text_content\\": \\"Herman Melville - Moby-Dick Availing himself of the mild, summer-cool weather that now reigned in these latitudes, and in preparation for the peculiarly active pursuits shortly to be anticipated, Perth, the begrimed, blistered old blacksmith, had not removed his portable forge to the hold again, after concluding his contributory work for Ahab\'s leg, but still retained it on deck, fast lashed to ringbolts by the foremast; being now almost incessantly invoked by the headsmen, and harpooneers, and bowsmen to do some little job for them; altering, or repairing, or new shaping their various weapons and boat furniture. Often he would be surrounded by an eager circle, all waiting to be served; holding boat-spades, pike-heads, harpoons, and lances, and jealously watching his every sooty movement, as he toiled. Nevertheless, this old man\'s was a patient hammer wielded by a patient arm. No murmur, no impatience, no petulance did come from him. Silent, slow, and solemn; bowing over still further his chronically broken back, he toiled away, as if toil were life itself, and the heavy beating of his hammer the heavy beating of his heart. And so it was.\xe2\x80\x94Most miserable! A peculiar walk in this old man, a certain slight but painful appearing yawing in his gait, had at an early period of the voyage excited the curiosity of the mariners. And to the importunity of their persisted questionings he had finally given in; and so it came to pass that every one now knew the shameful story of his wretched fate. Belated, and not innocently, one bitter winter\'s midnight, on the road running between two country towns, the blacksmith half-stupidly felt the deadly numbness stealing over him, and sought refuge in a leaning, dilapidated barn. The issue was, the loss of the extremities of both feet. Out of this revelation, part by part, at last came out the four acts of the gladness, and the one long, and as yet uncatastrophied fifth act of the grief of his life\'s drama. He was an old man, who, at the age of nearly sixty, had postponedly encountered that thing in sorrow\'s technicals called ruin. He had been an artisan of famed excellence, and with plenty to do; owned a house and garden; embraced a youthful, daughter-like, loving wife, and three blithe, ruddy children; every Sunday went to a cheerful-looking church, planted in a grove. But one night, under cover of darkness, and further concealed in a most cunning disguisement, a desperate burglar slid into his happy home, and robbed them all of everything. And darker yet to tell, the blacksmith himself did ignorantly conduct this burglar into his family\'s heart. It was the Bottle Conjuror! Upon the opening of that fatal cork, forth flew the fiend, and shrivelled up his home. Now, for prudent, most wise, and economic reasons, the blacksmith\'s shop was in the basement of his dwelling, but with a separate entrance to it; so that always had the young and loving healthy wife listened with no unhappy nervousness, but with vigorous pleasure, to the stout ringing of her young-armed old husband\'s hammer; whose reverberations, muffled by passing through the floors and walls, came up to her, not unsweetly, in her nursery; and so, to stout Labor\'s iron lullaby, the blacksmith\'s infants were rocked to slumber. Oh, woe on woe! Oh, Death, why canst thou not sometimes be timely? Hadst thou taken this old blacksmith to thyself ere his full ruin came upon him, then had the young widow had a delicious grief, and her orphans a truly venerable, legendary sire to dream of in their after years; and all of them a care-killing competency.\\",\\n  \\"main_content\\": \\"Herman Melville - Moby-Dick Availing himself of the mild, summer-cool weather that now reigned in these latitudes, and in preparation for the peculiarly active pursuits shortly to be anticipated, Perth, the begrimed, blistered old blacksmith, had not removed his portable forge to the hold again, after concluding his contributory work for Ahab\'s leg, but still retained it on deck, fast lashed to ringbolts by the foremast; being now almost incessantly invoked by the headsmen, and harpooneers, and bowsmen to do some little job for them; altering, or repairing, or new shaping their various weapons and boat furniture. Often he would be surrounded by an eager circle, all waiting to be served; holding boat-spades, pike-heads, harpoons, and lances, and jealously watching his every sooty movement, as he toiled. Nevertheless, this old man\'s was a patient hammer wielded by a patient arm. No murmur, no impatience, no petulance did come from him. Silent, slow, and solemn; bowing over still further his chronically broken back, he toiled away, as if toil were life itself, and the heavy beating of his hammer the heavy beating of his heart. And so it was.\xe2\x80\x94Most miserable! A peculiar walk in this old man, a certain slight but painful appearing yawing in his gait, had at an early period of the voyage excited the curiosity of the mariners. And to the importunity of their persisted questionings he had finally given in; and so it came to pass that every one now knew the shameful story of his wretched fate. Belated, and not innocently, one bitter winter\'s midnight, on the road running between two country towns, the blacksmith half-stupidly felt the deadly numbness stealing over him, and sought refuge in a leaning, dilapidated barn. The issue was, the loss of the extremities of both feet. Out of this revelation, part by part, at last came out the four acts of the gladness, and the one long, and as yet uncatastrophied fifth act of the grief of his life\'s drama. He was an old man, who, at the age of nearly sixty, had postponedly encountered that thing in sorrow\'s technicals called ruin. He had been an artisan of famed excellence, and with plenty to do; owned a house and garden; embraced a youthful, daughter-like, loving wife, and three blithe, ruddy children; every Sunday went to a cheerful-looking church, planted in a grove. But one night, under cover of darkness, and further concealed in a most cunning disguisement, a desperate burglar slid into his happy home, and robbed them all of everything. And darker yet to tell, the blacksmith himself did ignorantly conduct this burglar into his family\'s heart. It was the Bottle Conjuror! Upon the opening of that fatal cork, forth flew the fiend, and shrivelled up his home. Now, for prudent, most wise, and economic reasons, the blacksmith\'s shop was in the basement of his dwelling, but with a separate entrance to it; so that always had the young and loving healthy wife listened with no unhappy nervousness, but with vigorous pleasure, to the stout ringing of her young-armed old husband\'s hammer; whose reverberations, muffled by passing through the floors and walls, came up to her, not unsweetly, in her nursery; and so, to stout Labor\'s iron lullaby, the blacksmith\'s infants were rocked to slumber. Oh, woe on woe! Oh, Death, why canst thou not sometimes be timely? Hadst thou taken this old blacksmith to thyself ere his full ruin came upon him, then had the young widow had a delicious grief, and her orphans a truly venerable, legendary sire to dream of in their after years; and all of them a care-killing competency.\\",\\n  \\"meta_description\\": \\"\\",\\n  \\"total_images\\": 0,\\n  \\"analyzed_images\\": 0,\\n  \\"image_analyses\\": [],\\n  \\"timestamp\\": 178554.203\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 16:37:53,424 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 16:37:53,424 - root - DEBUG - [sse.py:159] - Client session disconnected d6e2122e-89b7-4c19-8d70-88572fafa97c
2025-06-07 16:39:09,761 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 16:39:09,762 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 16:39:09,763 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 16:39:09,764 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 16:39:09,764 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 16:39:09,764 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 16:39:09,765 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 16:39:09,765 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 16:39:09,766 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 16:39:09,766 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8000/sse
2025-06-07 16:39:09,767 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 16:39:09,767 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 16:39:09,767 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 16:39:11,209 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 16:39:11,209 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 16:39:11,214 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 16:39:11,216 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 16:39:11,217 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8000/sse
2025-06-07 16:39:12,928 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:39:12,929 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 72770556-1e6d-4d3b-bc92-957fb46d6830
2025-06-07 16:39:12,930 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:39:12,931 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:39:12,934 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:39:12,934 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=727705561e6d4d3bbc92957fb46d6830
2025-06-07 16:39:12,935 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=727705561e6d4d3bbc92957fb46d6830\r\n\r\n'
2025-06-07 16:39:22,074 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:39:22,074 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 72770556-1e6d-4d3b-bc92-957fb46d6830
2025-06-07 16:39:22,076 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":8}'
2025-06-07 16:39:22,077 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=8)
2025-06-07 16:39:22,077 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=8)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871A36F90>))
2025-06-07 16:39:22,081 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 16:39:25,081 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:39:25,083 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 4cebb998-4931-45e8-b02b-95b7a85f252b
2025-06-07 16:39:25,083 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:39:25,083 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:39:25,087 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:39:25,087 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=4cebb998493145e8b02b95b7a85f252b
2025-06-07 16:39:25,088 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=4cebb998493145e8b02b95b7a85f252b\r\n\r\n'
2025-06-07 16:39:40,098 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:39:40.098534+00:00\r\n\r\n'
2025-06-07 16:39:55,108 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:39:55.108001+00:00\r\n\r\n'
2025-06-07 16:39:59,773 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:39:59,773 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4cebb998-4931-45e8-b02b-95b7a85f252b
2025-06-07 16:39:59,774 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":9}'
2025-06-07 16:39:59,774 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=9)
2025-06-07 16:39:59,775 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=9)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871AE0EC0>))
2025-06-07 16:39:59,777 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 16:40:02,781 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:40:02,782 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 591e33a1-d88b-4708-8286-99ddc0e5a1db
2025-06-07 16:40:02,783 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:40:02,783 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:40:02,786 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:40:02,786 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=591e33a1d88b4708828699ddc0e5a1db
2025-06-07 16:40:02,786 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=591e33a1d88b4708828699ddc0e5a1db\r\n\r\n'
2025-06-07 16:40:17,764 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:40:17.764140+00:00\r\n\r\n'
2025-06-07 16:40:22,081 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:22,081 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 591e33a1-d88b-4708-8286-99ddc0e5a1db
2025-06-07 16:40:22,083 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"jsonrpc":"2.0","method":"notifications/cancelled","params":{"requestId":8,"reason":"McpError: MCP error -32001: Request timed out"}}'
2025-06-07 16:40:22,083 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 8, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')
2025-06-07 16:40:22,084 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 8, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871AE3350>))
2025-06-07 16:40:22,085 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:40:22,085 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:22,086 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:40:22,086 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:40:22,089 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:40:22,090 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=20873b7a4c004363acee9cfd878f2e0e
2025-06-07 16:40:22,090 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=20873b7a4c004363acee9cfd878f2e0e\r\n\r\n'
2025-06-07 16:40:22,091 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:22,093 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:22,093 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cherry Studio","version":"1.4.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 16:40:22,094 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)
2025-06-07 16:40:22,095 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871B10710>))
2025-06-07 16:40:22,096 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 16:40:22,097 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 16:40:22,099 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:22,099 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:22,099 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 16:40:22,100 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:40:22,100 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871B10920>))
2025-06-07 16:40:22,102 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:40:22,104 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:22,105 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:22,105 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 16:40:22,105 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 16:40:22,106 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871B116A0>))
2025-06-07 16:40:22,107 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871B10FB0>
2025-06-07 16:40:22,109 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 16:40:22,109 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 16:40:22,110 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:40:22,110 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 16:40:22,112 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 16:40:25,366 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:25,366 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:25,367 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":2}'
2025-06-07 16:40:25,368 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)
2025-06-07 16:40:25,368 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871B11760>))
2025-06-07 16:40:25,370 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871B11700>
2025-06-07 16:40:25,370 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 16:40:25,370 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 16:40:25,371 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:40:25,371 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=2, result={})), metadata=None)
2025-06-07 16:40:25,371 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":2,"result":{}}\r\n\r\n'
2025-06-07 16:40:25,375 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:25,375 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 16:40:25,375 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc","max_images":5}},"jsonrpc":"2.0","id":3}'
2025-06-07 16:40:25,376 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc', 'max_images': 5}}, jsonrpc='2.0', id=3)
2025-06-07 16:40:25,377 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc', 'max_images': 5}}, jsonrpc='2.0', id=3)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871B10CB0>))
2025-06-07 16:40:25,378 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000001487031CB60>
2025-06-07 16:40:25,379 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 16:40:25,379 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 16:40:25,379 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:40:25,380 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 16:40:25,380 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 16:40:29,641 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 16:40:29,986 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:40:32,779 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:40:32.779842+00:00\r\n\r\n'
2025-06-07 16:40:35,612 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:40:35,630 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 16:40:35,698 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 16:40:35,698 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 16:40:35,699 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 16:40:35,700 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 16:40:35,700 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 16:40:37,207 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 16:40:37,207 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i1.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_91b09fe3.jpg
2025-06-07 16:40:37,229 - PIL.Image - DEBUG - [Image.py:363] - Importing BlpImagePlugin
2025-06-07 16:40:37,231 - PIL.Image - DEBUG - [Image.py:363] - Importing BmpImagePlugin
2025-06-07 16:40:37,231 - PIL.Image - DEBUG - [Image.py:363] - Importing BufrStubImagePlugin
2025-06-07 16:40:37,232 - PIL.Image - DEBUG - [Image.py:363] - Importing CurImagePlugin
2025-06-07 16:40:37,233 - PIL.Image - DEBUG - [Image.py:363] - Importing DcxImagePlugin
2025-06-07 16:40:37,235 - PIL.Image - DEBUG - [Image.py:363] - Importing DdsImagePlugin
2025-06-07 16:40:37,240 - PIL.Image - DEBUG - [Image.py:363] - Importing EpsImagePlugin
2025-06-07 16:40:37,241 - PIL.Image - DEBUG - [Image.py:363] - Importing FitsImagePlugin
2025-06-07 16:40:37,242 - PIL.Image - DEBUG - [Image.py:363] - Importing FliImagePlugin
2025-06-07 16:40:37,244 - PIL.Image - DEBUG - [Image.py:363] - Importing FpxImagePlugin
2025-06-07 16:40:37,246 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import FpxImagePlugin: No module named 'olefile'
2025-06-07 16:40:37,246 - PIL.Image - DEBUG - [Image.py:363] - Importing FtexImagePlugin
2025-06-07 16:40:37,247 - PIL.Image - DEBUG - [Image.py:363] - Importing GbrImagePlugin
2025-06-07 16:40:37,248 - PIL.Image - DEBUG - [Image.py:363] - Importing GifImagePlugin
2025-06-07 16:40:37,248 - PIL.Image - DEBUG - [Image.py:363] - Importing GribStubImagePlugin
2025-06-07 16:40:37,249 - PIL.Image - DEBUG - [Image.py:363] - Importing Hdf5StubImagePlugin
2025-06-07 16:40:37,250 - PIL.Image - DEBUG - [Image.py:363] - Importing IcnsImagePlugin
2025-06-07 16:40:37,254 - PIL.Image - DEBUG - [Image.py:363] - Importing IcoImagePlugin
2025-06-07 16:40:37,255 - PIL.Image - DEBUG - [Image.py:363] - Importing ImImagePlugin
2025-06-07 16:40:37,256 - PIL.Image - DEBUG - [Image.py:363] - Importing ImtImagePlugin
2025-06-07 16:40:37,257 - PIL.Image - DEBUG - [Image.py:363] - Importing IptcImagePlugin
2025-06-07 16:40:37,259 - PIL.Image - DEBUG - [Image.py:363] - Importing JpegImagePlugin
2025-06-07 16:40:37,260 - PIL.Image - DEBUG - [Image.py:363] - Importing Jpeg2KImagePlugin
2025-06-07 16:40:37,260 - PIL.Image - DEBUG - [Image.py:363] - Importing McIdasImagePlugin
2025-06-07 16:40:37,261 - PIL.Image - DEBUG - [Image.py:363] - Importing MicImagePlugin
2025-06-07 16:40:37,262 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import MicImagePlugin: No module named 'olefile'
2025-06-07 16:40:37,263 - PIL.Image - DEBUG - [Image.py:363] - Importing MpegImagePlugin
2025-06-07 16:40:37,264 - PIL.Image - DEBUG - [Image.py:363] - Importing MpoImagePlugin
2025-06-07 16:40:37,268 - PIL.Image - DEBUG - [Image.py:363] - Importing MspImagePlugin
2025-06-07 16:40:37,270 - PIL.Image - DEBUG - [Image.py:363] - Importing PalmImagePlugin
2025-06-07 16:40:37,274 - PIL.Image - DEBUG - [Image.py:363] - Importing PcdImagePlugin
2025-06-07 16:40:37,275 - PIL.Image - DEBUG - [Image.py:363] - Importing PcxImagePlugin
2025-06-07 16:40:37,275 - PIL.Image - DEBUG - [Image.py:363] - Importing PdfImagePlugin
2025-06-07 16:40:37,281 - PIL.Image - DEBUG - [Image.py:363] - Importing PixarImagePlugin
2025-06-07 16:40:37,283 - PIL.Image - DEBUG - [Image.py:363] - Importing PngImagePlugin
2025-06-07 16:40:37,284 - PIL.Image - DEBUG - [Image.py:363] - Importing PpmImagePlugin
2025-06-07 16:40:37,284 - PIL.Image - DEBUG - [Image.py:363] - Importing PsdImagePlugin
2025-06-07 16:40:37,286 - PIL.Image - DEBUG - [Image.py:363] - Importing QoiImagePlugin
2025-06-07 16:40:37,287 - PIL.Image - DEBUG - [Image.py:363] - Importing SgiImagePlugin
2025-06-07 16:40:37,288 - PIL.Image - DEBUG - [Image.py:363] - Importing SpiderImagePlugin
2025-06-07 16:40:37,289 - PIL.Image - DEBUG - [Image.py:363] - Importing SunImagePlugin
2025-06-07 16:40:37,290 - PIL.Image - DEBUG - [Image.py:363] - Importing TgaImagePlugin
2025-06-07 16:40:37,291 - PIL.Image - DEBUG - [Image.py:363] - Importing TiffImagePlugin
2025-06-07 16:40:37,292 - PIL.Image - DEBUG - [Image.py:363] - Importing WebPImagePlugin
2025-06-07 16:40:37,295 - PIL.Image - DEBUG - [Image.py:363] - Importing WmfImagePlugin
2025-06-07 16:40:37,297 - PIL.Image - DEBUG - [Image.py:363] - Importing XbmImagePlugin
2025-06-07 16:40:37,298 - PIL.Image - DEBUG - [Image.py:363] - Importing XpmImagePlugin
2025-06-07 16:40:37,301 - PIL.Image - DEBUG - [Image.py:363] - Importing XVThumbImagePlugin
2025-06-07 16:40:37,321 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 16:40:37,322 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:40:37.322291+00:00\r\n\r\n'
2025-06-07 16:40:37,323 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000014871FB6930>
2025-06-07 16:40:37,324 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:40:37,325 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:40:37,325 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 16:40:37,325 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:40:37,326 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:40:37,326 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 16:40:37,326 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000014871E8D8D0> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 16:40:37,484 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000014871FB6B10>
2025-06-07 16:40:37,485 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 16:40:37,486 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:40:37,486 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 16:40:37,487 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:40:37,487 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 16:40:47,796 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:40:47.796489+00:00\r\n\r\n'
2025-06-07 16:40:52,332 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:40:52.332878+00:00\r\n\r\n'
2025-06-07 16:40:56,315 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 08:40:56 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2859'), (b'x-client-request-id', b'unknown-20250607164038-FETcUJCB'), (b'x-envoy-upstream-service-time', b'18432'), (b'x-request-id', b'021749285638656ae3896fadf431819d71188208c4cbf6a70fb0b')])
2025-06-07 16:40:56,316 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 16:40:56,316 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 16:40:56,317 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 16:40:56,317 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 16:40:56,317 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 16:40:56,319 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 16:40:56,321 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:40:56,321 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=3, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 16:40:56,322 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":3,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 16:40:59,776 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:59,777 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 591e33a1-d88b-4708-8286-99ddc0e5a1db
2025-06-07 16:40:59,778 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"jsonrpc":"2.0","method":"notifications/cancelled","params":{"requestId":9,"reason":"McpError: MCP error -32001: Request timed out"}}'
2025-06-07 16:40:59,778 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 9, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')
2025-06-07 16:40:59,779 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 9, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871A36D50>))
2025-06-07 16:40:59,781 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 16:40:59,781 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:40:59,781 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 16:40:59,782 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 16:40:59,786 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 16:40:59,787 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=1a345cf302d142b589268a1fbad87b94
2025-06-07 16:40:59,787 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=1a345cf302d142b589268a1fbad87b94\r\n\r\n'
2025-06-07 16:40:59,789 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:59,791 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:40:59,792 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cherry Studio","version":"1.4.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 16:40:59,792 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)
2025-06-07 16:40:59,792 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871FB7CB0>))
2025-06-07 16:40:59,793 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 16:40:59,794 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 16:40:59,795 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:59,796 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:40:59,796 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 16:40:59,797 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:40:59,797 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871FC82F0>))
2025-06-07 16:40:59,799 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 16:40:59,801 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:40:59,802 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:40:59,802 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 16:40:59,802 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 16:40:59,802 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871FC8A10>))
2025-06-07 16:40:59,804 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871FB6510>
2025-06-07 16:40:59,804 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 16:40:59,805 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 16:40:59,806 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:40:59,806 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 16:40:59,808 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 16:41:02,794 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:02.794540+00:00\r\n\r\n'
2025-06-07 16:41:03,434 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:41:03,434 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:41:03,435 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":2}'
2025-06-07 16:41:03,435 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)
2025-06-07 16:41:03,436 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871FB7E30>))
2025-06-07 16:41:03,438 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871FB7650>
2025-06-07 16:41:03,438 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 16:41:03,438 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 16:41:03,439 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:41:03,439 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=2, result={})), metadata=None)
2025-06-07 16:41:03,439 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":2,"result":{}}\r\n\r\n'
2025-06-07 16:41:03,443 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:41:03,443 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:41:03,444 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"}},"jsonrpc":"2.0","id":3}'
2025-06-07 16:41:03,444 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)
2025-06-07 16:41:03,444 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000014871FC8650>))
2025-06-07 16:41:03,447 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871FB7320>
2025-06-07 16:41:03,447 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 16:41:03,448 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 16:41:03,448 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:41:03,448 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 16:41:03,449 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 16:41:07,350 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:07.350386+00:00\r\n\r\n'
2025-06-07 16:41:07,638 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 16:41:07,968 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:41:13,604 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:41:13,624 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 16:41:13,694 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 16:41:13,694 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 16:41:13,696 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 16:41:13,696 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 16:41:13,697 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 16:41:15,177 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 16:41:15,177 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i2.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_ee53b9df.jpg
2025-06-07 16:41:15,191 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 16:41:15,193 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:15.193069+00:00\r\n\r\n'
2025-06-07 16:41:15,195 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000014872089850>
2025-06-07 16:41:15,195 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:41:15,196 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:41:15,196 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 16:41:15,197 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:41:15,197 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:41:15,197 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 16:41:15,197 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000014871F5A3D0> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 16:41:15,321 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000014872089970>
2025-06-07 16:41:15,322 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 16:41:15,323 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:41:15,323 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 16:41:15,324 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:41:15,324 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 16:41:17,821 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:17.821331+00:00\r\n\r\n'
2025-06-07 16:41:22,357 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:22.357034+00:00\r\n\r\n'
2025-06-07 16:41:30,187 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:30.187951+00:00\r\n\r\n'
2025-06-07 16:41:32,835 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:32.835314+00:00\r\n\r\n'
2025-06-07 16:41:36,634 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 08:41:37 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2925'), (b'x-client-request-id', b'unknown-20250607164117-GBIfJuxT'), (b'x-envoy-upstream-service-time', b'20254'), (b'x-request-id', b'021749285676492b135b6e0a6b7d38dc3c1ba50bb1a1668b7f54f')])
2025-06-07 16:41:36,634 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 16:41:36,635 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 16:41:36,635 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 16:41:36,636 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 16:41:36,636 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 16:41:36,636 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 16:41:36,638 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:41:36,638 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=3, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 16:41:36,639 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":3,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 16:41:37,371 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:37.371245+00:00\r\n\r\n'
2025-06-07 16:41:45,198 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:45.198984+00:00\r\n\r\n'
2025-06-07 16:41:47,849 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:47.849498+00:00\r\n\r\n'
2025-06-07 16:41:52,374 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:41:52.374982+00:00\r\n\r\n'
2025-06-07 16:42:00,212 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:00.212707+00:00\r\n\r\n'
2025-06-07 16:42:02,858 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:02.858638+00:00\r\n\r\n'
2025-06-07 16:42:07,376 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:07.376936+00:00\r\n\r\n'
2025-06-07 16:42:15,226 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:15.226581+00:00\r\n\r\n'
2025-06-07 16:42:17,881 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:17.881029+00:00\r\n\r\n'
2025-06-07 16:42:22,396 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:22.396763+00:00\r\n\r\n'
2025-06-07 16:42:29,196 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:42:29,197 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:42:29,198 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":4}'
2025-06-07 16:42:29,199 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)
2025-06-07 16:42:29,200 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000001487208A7E0>))
2025-06-07 16:42:29,201 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871FC8C80>
2025-06-07 16:42:29,203 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 16:42:29,203 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 16:42:29,205 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:42:29,205 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=4, result={})), metadata=None)
2025-06-07 16:42:29,206 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":4,"result":{}}\r\n\r\n'
2025-06-07 16:42:29,208 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 16:42:29,208 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 16:42:29,209 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"}},"jsonrpc":"2.0","id":5}'
2025-06-07 16:42:29,209 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=5)
2025-06-07 16:42:29,210 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=5)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000001487208A7B0>))
2025-06-07 16:42:29,211 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000014871FC8740>
2025-06-07 16:42:29,212 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 16:42:29,212 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 16:42:29,213 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:42:29,213 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 16:42:29,213 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 16:42:30,244 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:30.244348+00:00\r\n\r\n'
2025-06-07 16:42:32,887 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:32.887907+00:00\r\n\r\n'
2025-06-07 16:42:33,573 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 16:42:33,891 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:42:37,395 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:37.395198+00:00\r\n\r\n'
2025-06-07 16:42:39,009 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 16:42:39,027 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 16:42:39,106 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 16:42:39,107 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 16:42:39,109 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 16:42:39,109 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 16:42:39,109 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 16:42:40,694 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 16:42:40,695 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i2.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_ee53b9df.jpg
2025-06-07 16:42:40,710 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 16:42:40,712 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001487215A810>
2025-06-07 16:42:40,712 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:42:40,713 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:42:40,713 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 16:42:40,714 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:42:40,714 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 16:42:40,714 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 16:42:40,715 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000014872086A50> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 16:42:40,811 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001487215A9F0>
2025-06-07 16:42:40,811 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 16:42:40,813 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 16:42:40,813 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 16:42:40,814 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 16:42:40,814 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 16:42:45,258 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:45.258318+00:00\r\n\r\n'
2025-06-07 16:42:47,907 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:47.907804+00:00\r\n\r\n'
2025-06-07 16:42:52,402 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:42:52.402180+00:00\r\n\r\n'
2025-06-07 16:43:00,264 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:00.264705+00:00\r\n\r\n'
2025-06-07 16:43:02,907 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:02.907870+00:00\r\n\r\n'
2025-06-07 16:43:07,421 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:07.421969+00:00\r\n\r\n'
2025-06-07 16:43:15,292 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:15.292161+00:00\r\n\r\n'
2025-06-07 16:43:16,809 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 08:43:17 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'3266'), (b'x-client-request-id', b'unknown-20250607164242-JdtykiOw'), (b'x-envoy-upstream-service-time', b'35763'), (b'x-request-id', b'021749285761983acbe49a31755bec77b2f09448eb15fa978a3e6')])
2025-06-07 16:43:16,809 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 16:43:16,810 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 16:43:16,810 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 16:43:16,812 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 16:43:16,812 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 16:43:16,812 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 16:43:16,813 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 16:43:16,814 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=5, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 16:43:16,815 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":5,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 16:43:17,933 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:17.933620+00:00\r\n\r\n'
2025-06-07 16:43:22,446 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:22.446463+00:00\r\n\r\n'
2025-06-07 16:43:30,299 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:30.299524+00:00\r\n\r\n'
2025-06-07 16:43:32,953 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:32.953150+00:00\r\n\r\n'
2025-06-07 16:43:37,449 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:37.449633+00:00\r\n\r\n'
2025-06-07 16:43:45,317 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:45.317095+00:00\r\n\r\n'
2025-06-07 16:43:47,966 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:47.966258+00:00\r\n\r\n'
2025-06-07 16:43:52,469 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:43:52.469644+00:00\r\n\r\n'
2025-06-07 16:44:00,326 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:00.326835+00:00\r\n\r\n'
2025-06-07 16:44:02,983 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:02.983357+00:00\r\n\r\n'
2025-06-07 16:44:07,480 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:07.480342+00:00\r\n\r\n'
2025-06-07 16:44:15,346 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:15.346487+00:00\r\n\r\n'
2025-06-07 16:44:17,990 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:17.990660+00:00\r\n\r\n'
2025-06-07 16:44:22,501 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:22.501035+00:00\r\n\r\n'
2025-06-07 16:44:30,354 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:30.354957+00:00\r\n\r\n'
2025-06-07 16:44:33,001 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:33.001908+00:00\r\n\r\n'
2025-06-07 16:44:37,513 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:37.513800+00:00\r\n\r\n'
2025-06-07 16:44:45,364 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:45.364117+00:00\r\n\r\n'
2025-06-07 16:44:48,015 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:48.015785+00:00\r\n\r\n'
2025-06-07 16:44:52,531 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:44:52.531875+00:00\r\n\r\n'
2025-06-07 16:45:00,344 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:00.344623+00:00\r\n\r\n'
2025-06-07 16:45:03,031 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:03.031910+00:00\r\n\r\n'
2025-06-07 16:45:07,551 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:07.551050+00:00\r\n\r\n'
2025-06-07 16:45:15,369 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:15.369903+00:00\r\n\r\n'
2025-06-07 16:45:18,017 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:18.017222+00:00\r\n\r\n'
2025-06-07 16:45:22,567 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:22.567104+00:00\r\n\r\n'
2025-06-07 16:45:30,385 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:30.385671+00:00\r\n\r\n'
2025-06-07 16:45:33,028 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:33.028094+00:00\r\n\r\n'
2025-06-07 16:45:37,585 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:37.585028+00:00\r\n\r\n'
2025-06-07 16:45:45,400 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:45.400121+00:00\r\n\r\n'
2025-06-07 16:45:48,037 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:48.037858+00:00\r\n\r\n'
2025-06-07 16:45:52,599 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:45:52.599082+00:00\r\n\r\n'
2025-06-07 16:46:00,399 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:00.399753+00:00\r\n\r\n'
2025-06-07 16:46:03,044 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:03.044644+00:00\r\n\r\n'
2025-06-07 16:46:07,609 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:07.609596+00:00\r\n\r\n'
2025-06-07 16:46:15,390 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:15.390318+00:00\r\n\r\n'
2025-06-07 16:46:18,063 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:18.063380+00:00\r\n\r\n'
2025-06-07 16:46:22,615 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:22.615152+00:00\r\n\r\n'
2025-06-07 16:46:30,414 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:30.414195+00:00\r\n\r\n'
2025-06-07 16:46:33,086 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:33.086393+00:00\r\n\r\n'
2025-06-07 16:46:37,634 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:37.634509+00:00\r\n\r\n'
2025-06-07 16:46:45,421 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:45.421988+00:00\r\n\r\n'
2025-06-07 16:46:48,084 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:48.084421+00:00\r\n\r\n'
2025-06-07 16:46:52,628 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:46:52.628122+00:00\r\n\r\n'
2025-06-07 16:47:00,440 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:00.440176+00:00\r\n\r\n'
2025-06-07 16:47:03,091 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:03.091336+00:00\r\n\r\n'
2025-06-07 16:47:07,645 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:07.645133+00:00\r\n\r\n'
2025-06-07 16:47:15,449 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:15.449755+00:00\r\n\r\n'
2025-06-07 16:47:18,106 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:18.106272+00:00\r\n\r\n'
2025-06-07 16:47:22,667 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:22.667001+00:00\r\n\r\n'
2025-06-07 16:47:30,465 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:30.465389+00:00\r\n\r\n'
2025-06-07 16:47:33,128 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:33.128854+00:00\r\n\r\n'
2025-06-07 16:47:37,669 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:37.669824+00:00\r\n\r\n'
2025-06-07 16:47:45,473 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:45.473351+00:00\r\n\r\n'
2025-06-07 16:47:48,125 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:48.125834+00:00\r\n\r\n'
2025-06-07 16:47:52,671 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:47:52.671594+00:00\r\n\r\n'
2025-06-07 16:48:00,468 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:00.468431+00:00\r\n\r\n'
2025-06-07 16:48:03,152 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:03.152525+00:00\r\n\r\n'
2025-06-07 16:48:07,698 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:07.697902+00:00\r\n\r\n'
2025-06-07 16:48:15,484 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:15.483512+00:00\r\n\r\n'
2025-06-07 16:48:18,142 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:18.142458+00:00\r\n\r\n'
2025-06-07 16:48:22,686 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:22.686429+00:00\r\n\r\n'
2025-06-07 16:48:30,479 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:30.479726+00:00\r\n\r\n'
2025-06-07 16:48:33,139 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:33.139790+00:00\r\n\r\n'
2025-06-07 16:48:37,708 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:37.708614+00:00\r\n\r\n'
2025-06-07 16:48:45,495 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:45.495219+00:00\r\n\r\n'
2025-06-07 16:48:48,162 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:48.162320+00:00\r\n\r\n'
2025-06-07 16:48:52,729 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:48:52.729336+00:00\r\n\r\n'
2025-06-07 16:49:00,467 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:00.467681+00:00\r\n\r\n'
2025-06-07 16:49:03,178 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:03.178110+00:00\r\n\r\n'
2025-06-07 16:49:07,731 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:07.731680+00:00\r\n\r\n'
2025-06-07 16:49:15,487 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:15.487608+00:00\r\n\r\n'
2025-06-07 16:49:18,190 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:18.190917+00:00\r\n\r\n'
2025-06-07 16:49:22,753 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:22.753435+00:00\r\n\r\n'
2025-06-07 16:49:30,467 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:30.467419+00:00\r\n\r\n'
2025-06-07 16:49:33,179 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:33.179572+00:00\r\n\r\n'
2025-06-07 16:49:37,775 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:37.775595+00:00\r\n\r\n'
2025-06-07 16:49:45,484 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:45.484548+00:00\r\n\r\n'
2025-06-07 16:49:48,188 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:48.188513+00:00\r\n\r\n'
2025-06-07 16:49:52,775 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:49:52.775641+00:00\r\n\r\n'
2025-06-07 16:50:00,494 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:00.494426+00:00\r\n\r\n'
2025-06-07 16:50:03,197 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:03.197501+00:00\r\n\r\n'
2025-06-07 16:50:07,791 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:07.791566+00:00\r\n\r\n'
2025-06-07 16:50:15,507 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:15.507538+00:00\r\n\r\n'
2025-06-07 16:50:18,170 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:18.170390+00:00\r\n\r\n'
2025-06-07 16:50:22,794 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:22.794964+00:00\r\n\r\n'
2025-06-07 16:50:30,500 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:30.500977+00:00\r\n\r\n'
2025-06-07 16:50:33,178 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:33.178196+00:00\r\n\r\n'
2025-06-07 16:50:37,813 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:37.813747+00:00\r\n\r\n'
2025-06-07 16:50:45,525 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:45.525208+00:00\r\n\r\n'
2025-06-07 16:50:48,193 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:48.193520+00:00\r\n\r\n'
2025-06-07 16:50:52,797 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:50:52.797947+00:00\r\n\r\n'
2025-06-07 16:51:00,527 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:00.527190+00:00\r\n\r\n'
2025-06-07 16:51:22,071 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:22.071997+00:00\r\n\r\n'
2025-06-07 16:51:22,085 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:22.085506+00:00\r\n\r\n'
2025-06-07 16:51:37,073 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:37.071223+00:00\r\n\r\n'
2025-06-07 16:51:37,076 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:37.076305+00:00\r\n\r\n'
2025-06-07 16:51:37,077 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:37.077375+00:00\r\n\r\n'
2025-06-07 16:51:52,083 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:52.083817+00:00\r\n\r\n'
2025-06-07 16:51:52,084 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:52.084824+00:00\r\n\r\n'
2025-06-07 16:51:52,086 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:51:52.086822+00:00\r\n\r\n'
2025-06-07 16:52:07,091 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:07.091973+00:00\r\n\r\n'
2025-06-07 16:52:07,092 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:07.092977+00:00\r\n\r\n'
2025-06-07 16:52:07,093 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:07.093975+00:00\r\n\r\n'
2025-06-07 16:52:22,118 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:22.118938+00:00\r\n\r\n'
2025-06-07 16:52:22,119 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:22.119941+00:00\r\n\r\n'
2025-06-07 16:52:22,120 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:22.120939+00:00\r\n\r\n'
2025-06-07 16:52:37,141 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:37.141816+00:00\r\n\r\n'
2025-06-07 16:52:37,143 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:37.143817+00:00\r\n\r\n'
2025-06-07 16:52:37,145 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:37.145816+00:00\r\n\r\n'
2025-06-07 16:52:52,145 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:52.145796+00:00\r\n\r\n'
2025-06-07 16:52:52,147 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:52.147796+00:00\r\n\r\n'
2025-06-07 16:52:52,149 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:52:52.149795+00:00\r\n\r\n'
2025-06-07 16:53:07,154 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:07.154723+00:00\r\n\r\n'
2025-06-07 16:53:07,156 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:07.156210+00:00\r\n\r\n'
2025-06-07 16:53:07,157 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:07.157222+00:00\r\n\r\n'
2025-06-07 16:53:22,171 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:22.171351+00:00\r\n\r\n'
2025-06-07 16:53:22,171 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:22.171351+00:00\r\n\r\n'
2025-06-07 16:53:22,172 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:22.172656+00:00\r\n\r\n'
2025-06-07 16:53:37,171 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:37.171407+00:00\r\n\r\n'
2025-06-07 16:53:37,172 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:37.172920+00:00\r\n\r\n'
2025-06-07 16:53:37,173 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:37.173919+00:00\r\n\r\n'
2025-06-07 16:53:52,173 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:52.173821+00:00\r\n\r\n'
2025-06-07 16:53:52,174 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:52.174328+00:00\r\n\r\n'
2025-06-07 16:53:52,175 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:53:52.175438+00:00\r\n\r\n'
2025-06-07 16:54:07,189 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:07.189681+00:00\r\n\r\n'
2025-06-07 16:54:07,190 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:07.190686+00:00\r\n\r\n'
2025-06-07 16:54:07,192 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:07.192162+00:00\r\n\r\n'
2025-06-07 16:54:22,207 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:22.207344+00:00\r\n\r\n'
2025-06-07 16:54:22,208 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:22.208633+00:00\r\n\r\n'
2025-06-07 16:54:22,209 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:22.209640+00:00\r\n\r\n'
2025-06-07 16:54:37,228 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:37.228219+00:00\r\n\r\n'
2025-06-07 16:54:37,228 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:37.228219+00:00\r\n\r\n'
2025-06-07 16:54:37,229 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:37.229720+00:00\r\n\r\n'
2025-06-07 16:54:52,239 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:52.239583+00:00\r\n\r\n'
2025-06-07 16:54:52,239 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:52.239583+00:00\r\n\r\n'
2025-06-07 16:54:52,240 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:54:52.240879+00:00\r\n\r\n'
2025-06-07 16:55:07,241 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:07.241805+00:00\r\n\r\n'
2025-06-07 16:55:07,243 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:07.243043+00:00\r\n\r\n'
2025-06-07 16:55:07,243 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:07.243043+00:00\r\n\r\n'
2025-06-07 16:55:22,248 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:22.248066+00:00\r\n\r\n'
2025-06-07 16:55:22,250 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:22.250067+00:00\r\n\r\n'
2025-06-07 16:55:22,251 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:22.251067+00:00\r\n\r\n'
2025-06-07 16:55:37,267 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:37.267071+00:00\r\n\r\n'
2025-06-07 16:55:37,268 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:37.268399+00:00\r\n\r\n'
2025-06-07 16:55:37,269 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:37.269408+00:00\r\n\r\n'
2025-06-07 16:55:52,282 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:52.282118+00:00\r\n\r\n'
2025-06-07 16:55:52,282 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:52.282627+00:00\r\n\r\n'
2025-06-07 16:55:52,283 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:55:52.283692+00:00\r\n\r\n'
2025-06-07 16:56:07,271 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:07.271944+00:00\r\n\r\n'
2025-06-07 16:56:07,273 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:07.273019+00:00\r\n\r\n'
2025-06-07 16:56:07,274 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:07.274041+00:00\r\n\r\n'
2025-06-07 16:56:22,280 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:22.280116+00:00\r\n\r\n'
2025-06-07 16:56:22,280 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:22.280661+00:00\r\n\r\n'
2025-06-07 16:56:22,282 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:22.282120+00:00\r\n\r\n'
2025-06-07 16:56:37,301 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:37.301163+00:00\r\n\r\n'
2025-06-07 16:56:37,302 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:37.302673+00:00\r\n\r\n'
2025-06-07 16:56:37,303 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:37.303178+00:00\r\n\r\n'
2025-06-07 16:56:52,314 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:52.314931+00:00\r\n\r\n'
2025-06-07 16:56:52,316 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:52.316475+00:00\r\n\r\n'
2025-06-07 16:56:52,317 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:56:52.317481+00:00\r\n\r\n'
2025-06-07 16:57:07,337 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:07.337439+00:00\r\n\r\n'
2025-06-07 16:57:07,337 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:07.337439+00:00\r\n\r\n'
2025-06-07 16:57:07,339 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:07.339744+00:00\r\n\r\n'
2025-06-07 16:57:22,350 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:22.350270+00:00\r\n\r\n'
2025-06-07 16:57:22,350 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:22.350270+00:00\r\n\r\n'
2025-06-07 16:57:22,351 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:22.351465+00:00\r\n\r\n'
2025-06-07 16:57:37,363 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:37.363364+00:00\r\n\r\n'
2025-06-07 16:57:37,363 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:37.363873+00:00\r\n\r\n'
2025-06-07 16:57:37,365 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:37.365914+00:00\r\n\r\n'
2025-06-07 16:57:52,363 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:52.363270+00:00\r\n\r\n'
2025-06-07 16:57:52,363 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:52.363775+00:00\r\n\r\n'
2025-06-07 16:57:52,364 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:57:52.364910+00:00\r\n\r\n'
2025-06-07 16:58:07,376 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:07.376506+00:00\r\n\r\n'
2025-06-07 16:58:07,377 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:07.377506+00:00\r\n\r\n'
2025-06-07 16:58:07,378 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:07.378509+00:00\r\n\r\n'
2025-06-07 16:58:22,394 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:22.394726+00:00\r\n\r\n'
2025-06-07 16:58:22,395 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:22.395233+00:00\r\n\r\n'
2025-06-07 16:58:22,396 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:22.396825+00:00\r\n\r\n'
2025-06-07 16:58:37,389 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:37.389270+00:00\r\n\r\n'
2025-06-07 16:58:37,389 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:37.389778+00:00\r\n\r\n'
2025-06-07 16:58:37,391 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:37.391188+00:00\r\n\r\n'
2025-06-07 16:58:52,404 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:52.404314+00:00\r\n\r\n'
2025-06-07 16:58:52,404 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:52.404819+00:00\r\n\r\n'
2025-06-07 16:58:52,405 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:58:52.405878+00:00\r\n\r\n'
2025-06-07 16:59:07,402 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:07.402753+00:00\r\n\r\n'
2025-06-07 16:59:07,403 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:07.403261+00:00\r\n\r\n'
2025-06-07 16:59:07,404 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:07.404791+00:00\r\n\r\n'
2025-06-07 16:59:22,397 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:22.397966+00:00\r\n\r\n'
2025-06-07 16:59:22,397 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:22.397966+00:00\r\n\r\n'
2025-06-07 16:59:22,399 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:22.399460+00:00\r\n\r\n'
2025-06-07 16:59:37,408 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:37.408810+00:00\r\n\r\n'
2025-06-07 16:59:37,409 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:37.409281+00:00\r\n\r\n'
2025-06-07 16:59:37,410 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:37.410557+00:00\r\n\r\n'
2025-06-07 16:59:52,404 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:52.404389+00:00\r\n\r\n'
2025-06-07 16:59:52,404 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:52.404897+00:00\r\n\r\n'
2025-06-07 16:59:52,406 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 08:59:52.406195+00:00\r\n\r\n'
2025-06-07 17:00:07,412 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:07.412679+00:00\r\n\r\n'
2025-06-07 17:00:07,412 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:07.412679+00:00\r\n\r\n'
2025-06-07 17:00:07,414 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:07.414183+00:00\r\n\r\n'
2025-06-07 17:00:22,419 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:22.419090+00:00\r\n\r\n'
2025-06-07 17:00:22,419 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:22.419090+00:00\r\n\r\n'
2025-06-07 17:00:22,421 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:22.421376+00:00\r\n\r\n'
2025-06-07 17:00:37,436 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:37.436156+00:00\r\n\r\n'
2025-06-07 17:00:37,436 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:37.436156+00:00\r\n\r\n'
2025-06-07 17:00:37,437 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:37.437601+00:00\r\n\r\n'
2025-06-07 17:00:52,438 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:52.438227+00:00\r\n\r\n'
2025-06-07 17:00:52,439 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:52.439429+00:00\r\n\r\n'
2025-06-07 17:00:52,440 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:00:52.440495+00:00\r\n\r\n'
2025-06-07 17:01:07,454 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:07.454676+00:00\r\n\r\n'
2025-06-07 17:01:07,454 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:07.454676+00:00\r\n\r\n'
2025-06-07 17:01:07,455 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:07.455681+00:00\r\n\r\n'
2025-06-07 17:01:22,454 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:22.454013+00:00\r\n\r\n'
2025-06-07 17:01:22,455 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:22.455019+00:00\r\n\r\n'
2025-06-07 17:01:22,455 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:22.455019+00:00\r\n\r\n'
2025-06-07 17:01:37,462 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:37.462876+00:00\r\n\r\n'
2025-06-07 17:01:37,464 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:37.464077+00:00\r\n\r\n'
2025-06-07 17:01:37,465 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:37.465099+00:00\r\n\r\n'
2025-06-07 17:01:52,496 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:52.496116+00:00\r\n\r\n'
2025-06-07 17:01:52,496 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:52.496622+00:00\r\n\r\n'
2025-06-07 17:01:52,497 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:01:52.497945+00:00\r\n\r\n'
2025-06-07 17:02:07,513 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:07.513904+00:00\r\n\r\n'
2025-06-07 17:02:07,514 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:07.514410+00:00\r\n\r\n'
2025-06-07 17:02:07,515 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:07.515414+00:00\r\n\r\n'
2025-06-07 17:02:22,539 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:22.539050+00:00\r\n\r\n'
2025-06-07 17:02:22,540 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:22.540052+00:00\r\n\r\n'
2025-06-07 17:02:22,541 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:22.541273+00:00\r\n\r\n'
2025-06-07 17:02:37,544 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:37.544696+00:00\r\n\r\n'
2025-06-07 17:02:37,545 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:37.545699+00:00\r\n\r\n'
2025-06-07 17:02:37,547 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:37.547188+00:00\r\n\r\n'
2025-06-07 17:02:52,568 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:52.568931+00:00\r\n\r\n'
2025-06-07 17:02:52,569 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:52.569931+00:00\r\n\r\n'
2025-06-07 17:02:52,571 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:02:52.571931+00:00\r\n\r\n'
2025-06-07 17:03:07,584 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:07.584179+00:00\r\n\r\n'
2025-06-07 17:03:07,584 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:07.584686+00:00\r\n\r\n'
2025-06-07 17:03:07,585 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:07.585728+00:00\r\n\r\n'
2025-06-07 17:03:22,589 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:22.589537+00:00\r\n\r\n'
2025-06-07 17:03:22,590 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:22.590368+00:00\r\n\r\n'
2025-06-07 17:03:22,591 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:22.591810+00:00\r\n\r\n'
2025-06-07 17:03:37,608 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:37.608041+00:00\r\n\r\n'
2025-06-07 17:03:38,305 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:38.305536+00:00\r\n\r\n'
2025-06-07 17:03:38,325 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:38.324132+00:00\r\n\r\n'
2025-06-07 17:03:53,483 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:53.483790+00:00\r\n\r\n'
2025-06-07 17:03:53,485 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:53.485330+00:00\r\n\r\n'
2025-06-07 17:03:53,487 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:03:53.487375+00:00\r\n\r\n'
2025-06-07 17:04:08,494 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:08.494453+00:00\r\n\r\n'
2025-06-07 17:04:08,494 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:08.494959+00:00\r\n\r\n'
2025-06-07 17:04:08,496 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:08.496049+00:00\r\n\r\n'
2025-06-07 17:04:23,527 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:23.527280+00:00\r\n\r\n'
2025-06-07 17:04:23,527 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:23.527786+00:00\r\n\r\n'
2025-06-07 17:04:23,530 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:23.528793+00:00\r\n\r\n'
2025-06-07 17:04:38,549 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:38.549022+00:00\r\n\r\n'
2025-06-07 17:04:38,550 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:38.550024+00:00\r\n\r\n'
2025-06-07 17:04:38,551 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:38.551029+00:00\r\n\r\n'
2025-06-07 17:04:53,566 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:53.566734+00:00\r\n\r\n'
2025-06-07 17:04:53,569 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:53.569629+00:00\r\n\r\n'
2025-06-07 17:04:53,570 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:04:53.570936+00:00\r\n\r\n'
2025-06-07 17:05:08,561 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:08.561438+00:00\r\n\r\n'
2025-06-07 17:05:08,562 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:08.562442+00:00\r\n\r\n'
2025-06-07 17:05:08,563 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:08.563733+00:00\r\n\r\n'
2025-06-07 17:05:23,577 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:23.577899+00:00\r\n\r\n'
2025-06-07 17:05:23,579 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:23.579076+00:00\r\n\r\n'
2025-06-07 17:05:23,580 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:23.580579+00:00\r\n\r\n'
2025-06-07 17:05:38,597 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:38.597588+00:00\r\n\r\n'
2025-06-07 17:05:38,598 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:38.598589+00:00\r\n\r\n'
2025-06-07 17:05:38,599 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:05:38.599591+00:00\r\n\r\n'
2025-06-07 17:05:39,714 - root - DEBUG - [sse.py:159] - Client session disconnected 591e33a1-d88b-4708-8286-99ddc0e5a1db
2025-06-07 17:05:39,714 - root - DEBUG - [sse.py:159] - Client session disconnected 20873b7a-4c00-4363-acee-9cfd878f2e0e
2025-06-07 17:05:39,715 - root - DEBUG - [sse.py:159] - Client session disconnected 1a345cf3-02d1-42b5-8926-8a1fbad87b94
2025-06-07 17:05:55,606 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 17:05:55,608 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 17:05:55,608 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 17:05:55,608 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 17:05:55,609 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 17:05:55,609 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 17:05:55,609 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 17:05:55,609 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 17:05:55,613 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 17:05:55,613 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8000/sse
2025-06-07 17:05:55,614 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 17:05:55,614 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 17:05:55,615 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 17:05:57,230 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 17:05:57,230 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 17:05:57,238 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 17:05:57,240 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 17:05:57,241 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8000/sse
2025-06-07 17:05:57,807 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:05:57,807 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 1b8188d0-5651-4e6b-8f12-fca9ce281290
2025-06-07 17:05:57,808 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:05:57,808 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:05:57,812 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:05:57,812 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 27e37772-4a8a-43ac-b7ac-aa0347612746
2025-06-07 17:05:57,813 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:05:57,813 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:05:57,815 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:05:57,815 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 17212680-fe8b-4fb5-b95b-9dcc6f71d45f
2025-06-07 17:05:57,815 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:05:57,816 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:05:57,819 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:05:57,820 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=1b8188d056514e6b8f12fca9ce281290
2025-06-07 17:05:57,821 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:05:57,822 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:05:57,823 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=1b8188d056514e6b8f12fca9ce281290\r\n\r\n'
2025-06-07 17:05:57,823 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=27e377724a8a43acb7acaa0347612746
2025-06-07 17:05:57,824 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=17212680fe8b4fb5b95b9dcc6f71d45f
2025-06-07 17:05:57,824 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=27e377724a8a43acb7acaa0347612746\r\n\r\n'
2025-06-07 17:05:57,824 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=17212680fe8b4fb5b95b9dcc6f71d45f\r\n\r\n'
2025-06-07 17:06:04,728 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:06:04,728 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 1b8188d0-5651-4e6b-8f12-fca9ce281290
2025-06-07 17:06:04,729 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":6}'
2025-06-07 17:06:04,730 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=6)
2025-06-07 17:06:04,730 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=6)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000027552276E40>))
2025-06-07 17:06:04,732 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 17:06:07,746 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:06:07,746 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: d7f3925a-42eb-4bea-9b92-47f637e6c1f2
2025-06-07 17:06:07,748 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:06:07,748 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:06:07,752 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:06:07,752 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=d7f3925a42eb4bea9b9247f637e6c1f2
2025-06-07 17:06:07,752 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=d7f3925a42eb4bea9b9247f637e6c1f2\r\n\r\n'
2025-06-07 17:06:12,826 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:12.826932+00:00\r\n\r\n'
2025-06-07 17:06:12,827 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:12.827934+00:00\r\n\r\n'
2025-06-07 17:06:22,769 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:22.769823+00:00\r\n\r\n'
2025-06-07 17:06:27,848 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:27.848991+00:00\r\n\r\n'
2025-06-07 17:06:27,850 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:27.850001+00:00\r\n\r\n'
2025-06-07 17:06:37,775 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:37.775013+00:00\r\n\r\n'
2025-06-07 17:06:42,855 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:42.855021+00:00\r\n\r\n'
2025-06-07 17:06:42,856 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:42.856440+00:00\r\n\r\n'
2025-06-07 17:06:52,789 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:52.789031+00:00\r\n\r\n'
2025-06-07 17:06:57,882 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:57.882865+00:00\r\n\r\n'
2025-06-07 17:06:57,882 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:06:57.882865+00:00\r\n\r\n'
2025-06-07 17:07:04,740 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:04,741 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d7f3925a-42eb-4bea-9b92-47f637e6c1f2
2025-06-07 17:07:04,742 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"jsonrpc":"2.0","method":"notifications/cancelled","params":{"requestId":6,"reason":"McpError: MCP error -32001: Request timed out"}}'
2025-06-07 17:07:04,742 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 6, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')
2025-06-07 17:07:04,742 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 6, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002755232A4E0>))
2025-06-07 17:07:04,744 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:07:04,745 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:04,745 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:07:04,746 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:07:04,749 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:07:04,749 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=d935b6cbd6fe404882ba280c2545288f
2025-06-07 17:07:04,750 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=d935b6cbd6fe404882ba280c2545288f\r\n\r\n'
2025-06-07 17:07:04,752 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:04,752 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:04,752 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cherry Studio","version":"1.4.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 17:07:04,753 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)
2025-06-07 17:07:04,753 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000027552329910>))
2025-06-07 17:07:04,754 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 17:07:04,756 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 17:07:04,759 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:04,759 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:04,759 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 17:07:04,760 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 17:07:04,760 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002755232B740>))
2025-06-07 17:07:04,761 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 17:07:04,764 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:04,764 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:04,764 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 17:07:04,766 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 17:07:04,766 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x00000275523545C0>))
2025-06-07 17:07:04,767 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000027552251A30>
2025-06-07 17:07:04,767 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 17:07:04,768 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 17:07:04,769 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:07:04,769 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 17:07:04,770 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 17:07:07,769 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:07.769987+00:00\r\n\r\n'
2025-06-07 17:07:08,373 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:08,373 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:08,375 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":2}'
2025-06-07 17:07:08,376 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)
2025-06-07 17:07:08,376 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002755232BF20>))
2025-06-07 17:07:08,377 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x00000275522537D0>
2025-06-07 17:07:08,377 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 17:07:08,377 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 17:07:08,378 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:07:08,378 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=2, result={})), metadata=None)
2025-06-07 17:07:08,378 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":2,"result":{}}\r\n\r\n'
2025-06-07 17:07:08,382 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:08,382 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:08,382 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"}},"jsonrpc":"2.0","id":3}'
2025-06-07 17:07:08,383 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)
2025-06-07 17:07:08,383 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000027552354200>))
2025-06-07 17:07:08,385 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000027552191D00>
2025-06-07 17:07:08,385 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 17:07:08,385 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 17:07:08,385 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:07:08,387 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 17:07:08,387 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 17:07:12,893 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:12.893858+00:00\r\n\r\n'
2025-06-07 17:07:12,894 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:12.894859+00:00\r\n\r\n'
2025-06-07 17:07:12,895 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 17:07:13,251 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:07:18,710 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:07:18,730 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 17:07:18,797 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 17:07:18,797 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 17:07:18,798 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 17:07:18,798 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 17:07:18,799 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 17:07:20,188 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 17:07:20,188 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i1.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_91b09fe3.jpg
2025-06-07 17:07:20,225 - PIL.Image - DEBUG - [Image.py:363] - Importing BlpImagePlugin
2025-06-07 17:07:20,227 - PIL.Image - DEBUG - [Image.py:363] - Importing BmpImagePlugin
2025-06-07 17:07:20,227 - PIL.Image - DEBUG - [Image.py:363] - Importing BufrStubImagePlugin
2025-06-07 17:07:20,228 - PIL.Image - DEBUG - [Image.py:363] - Importing CurImagePlugin
2025-06-07 17:07:20,230 - PIL.Image - DEBUG - [Image.py:363] - Importing DcxImagePlugin
2025-06-07 17:07:20,232 - PIL.Image - DEBUG - [Image.py:363] - Importing DdsImagePlugin
2025-06-07 17:07:20,235 - PIL.Image - DEBUG - [Image.py:363] - Importing EpsImagePlugin
2025-06-07 17:07:20,239 - PIL.Image - DEBUG - [Image.py:363] - Importing FitsImagePlugin
2025-06-07 17:07:20,240 - PIL.Image - DEBUG - [Image.py:363] - Importing FliImagePlugin
2025-06-07 17:07:20,242 - PIL.Image - DEBUG - [Image.py:363] - Importing FpxImagePlugin
2025-06-07 17:07:20,244 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import FpxImagePlugin: No module named 'olefile'
2025-06-07 17:07:20,244 - PIL.Image - DEBUG - [Image.py:363] - Importing FtexImagePlugin
2025-06-07 17:07:20,245 - PIL.Image - DEBUG - [Image.py:363] - Importing GbrImagePlugin
2025-06-07 17:07:20,248 - PIL.Image - DEBUG - [Image.py:363] - Importing GifImagePlugin
2025-06-07 17:07:20,248 - PIL.Image - DEBUG - [Image.py:363] - Importing GribStubImagePlugin
2025-06-07 17:07:20,249 - PIL.Image - DEBUG - [Image.py:363] - Importing Hdf5StubImagePlugin
2025-06-07 17:07:20,250 - PIL.Image - DEBUG - [Image.py:363] - Importing IcnsImagePlugin
2025-06-07 17:07:20,255 - PIL.Image - DEBUG - [Image.py:363] - Importing IcoImagePlugin
2025-06-07 17:07:20,256 - PIL.Image - DEBUG - [Image.py:363] - Importing ImImagePlugin
2025-06-07 17:07:20,258 - PIL.Image - DEBUG - [Image.py:363] - Importing ImtImagePlugin
2025-06-07 17:07:20,259 - PIL.Image - DEBUG - [Image.py:363] - Importing IptcImagePlugin
2025-06-07 17:07:20,262 - PIL.Image - DEBUG - [Image.py:363] - Importing JpegImagePlugin
2025-06-07 17:07:20,262 - PIL.Image - DEBUG - [Image.py:363] - Importing Jpeg2KImagePlugin
2025-06-07 17:07:20,262 - PIL.Image - DEBUG - [Image.py:363] - Importing McIdasImagePlugin
2025-06-07 17:07:20,264 - PIL.Image - DEBUG - [Image.py:363] - Importing MicImagePlugin
2025-06-07 17:07:20,266 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import MicImagePlugin: No module named 'olefile'
2025-06-07 17:07:20,266 - PIL.Image - DEBUG - [Image.py:363] - Importing MpegImagePlugin
2025-06-07 17:07:20,269 - PIL.Image - DEBUG - [Image.py:363] - Importing MpoImagePlugin
2025-06-07 17:07:20,274 - PIL.Image - DEBUG - [Image.py:363] - Importing MspImagePlugin
2025-06-07 17:07:20,276 - PIL.Image - DEBUG - [Image.py:363] - Importing PalmImagePlugin
2025-06-07 17:07:20,277 - PIL.Image - DEBUG - [Image.py:363] - Importing PcdImagePlugin
2025-06-07 17:07:20,279 - PIL.Image - DEBUG - [Image.py:363] - Importing PcxImagePlugin
2025-06-07 17:07:20,279 - PIL.Image - DEBUG - [Image.py:363] - Importing PdfImagePlugin
2025-06-07 17:07:20,286 - PIL.Image - DEBUG - [Image.py:363] - Importing PixarImagePlugin
2025-06-07 17:07:20,287 - PIL.Image - DEBUG - [Image.py:363] - Importing PngImagePlugin
2025-06-07 17:07:20,288 - PIL.Image - DEBUG - [Image.py:363] - Importing PpmImagePlugin
2025-06-07 17:07:20,288 - PIL.Image - DEBUG - [Image.py:363] - Importing PsdImagePlugin
2025-06-07 17:07:20,289 - PIL.Image - DEBUG - [Image.py:363] - Importing QoiImagePlugin
2025-06-07 17:07:20,290 - PIL.Image - DEBUG - [Image.py:363] - Importing SgiImagePlugin
2025-06-07 17:07:20,292 - PIL.Image - DEBUG - [Image.py:363] - Importing SpiderImagePlugin
2025-06-07 17:07:20,295 - PIL.Image - DEBUG - [Image.py:363] - Importing SunImagePlugin
2025-06-07 17:07:20,296 - PIL.Image - DEBUG - [Image.py:363] - Importing TgaImagePlugin
2025-06-07 17:07:20,297 - PIL.Image - DEBUG - [Image.py:363] - Importing TiffImagePlugin
2025-06-07 17:07:20,297 - PIL.Image - DEBUG - [Image.py:363] - Importing WebPImagePlugin
2025-06-07 17:07:20,300 - PIL.Image - DEBUG - [Image.py:363] - Importing WmfImagePlugin
2025-06-07 17:07:20,304 - PIL.Image - DEBUG - [Image.py:363] - Importing XbmImagePlugin
2025-06-07 17:07:20,305 - PIL.Image - DEBUG - [Image.py:363] - Importing XpmImagePlugin
2025-06-07 17:07:20,307 - PIL.Image - DEBUG - [Image.py:363] - Importing XVThumbImagePlugin
2025-06-07 17:07:20,322 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 17:07:20,323 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:20.323438+00:00\r\n\r\n'
2025-06-07 17:07:20,325 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000275527F2330>
2025-06-07 17:07:20,326 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:07:20,328 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:07:20,328 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 17:07:20,328 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:07:20,328 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:07:20,329 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 17:07:20,329 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000275526BB5D0> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 17:07:20,438 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000275527F26C0>
2025-06-07 17:07:20,438 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 17:07:20,439 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:07:20,439 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 17:07:20,440 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:07:20,440 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 17:07:22,777 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:22.777857+00:00\r\n\r\n'
2025-06-07 17:07:27,914 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:27.914636+00:00\r\n\r\n'
2025-06-07 17:07:27,914 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:27.914636+00:00\r\n\r\n'
2025-06-07 17:07:35,326 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:35.326803+00:00\r\n\r\n'
2025-06-07 17:07:37,770 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:37.770501+00:00\r\n\r\n'
2025-06-07 17:07:42,926 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:42.926323+00:00\r\n\r\n'
2025-06-07 17:07:42,926 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:42.926831+00:00\r\n\r\n'
2025-06-07 17:07:50,337 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:50.337657+00:00\r\n\r\n'
2025-06-07 17:07:52,076 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 09:07:52 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2918'), (b'x-client-request-id', b'unknown-20250607170721-dfqmLswh'), (b'x-envoy-upstream-service-time', b'31361'), (b'x-request-id', b'021749287241618411cd377557adf775541642e5855eb35f4481f')])
2025-06-07 17:07:52,078 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 17:07:52,078 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 17:07:52,079 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 17:07:52,079 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 17:07:52,080 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 17:07:52,080 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 17:07:52,083 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:07:52,083 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=3, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 17:07:52,084 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":3,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 17:07:52,782 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:52.782590+00:00\r\n\r\n'
2025-06-07 17:07:55,560 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:55,561 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:55,561 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":4}'
2025-06-07 17:07:55,562 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)
2025-06-07 17:07:55,562 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x00000275526B5E80>))
2025-06-07 17:07:55,565 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000002755232B7D0>
2025-06-07 17:07:55,565 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 17:07:55,565 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 17:07:55,565 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:07:55,566 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=4, result={})), metadata=None)
2025-06-07 17:07:55,566 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":4,"result":{}}\r\n\r\n'
2025-06-07 17:07:55,568 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:07:55,568 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:07:55,570 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc","max_images":5}},"jsonrpc":"2.0","id":5}'
2025-06-07 17:07:55,570 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc', 'max_images': 5}}, jsonrpc='2.0', id=5)
2025-06-07 17:07:55,571 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc', 'max_images': 5}}, jsonrpc='2.0', id=5)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x00000275526B5CA0>))
2025-06-07 17:07:55,572 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x00000275520ABF80>
2025-06-07 17:07:55,572 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 17:07:55,573 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 17:07:55,573 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:07:55,574 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 17:07:55,574 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 17:07:57,929 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:57.929164+00:00\r\n\r\n'
2025-06-07 17:07:57,929 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:07:57.929164+00:00\r\n\r\n'
2025-06-07 17:07:59,501 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 17:07:59,790 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:08:05,353 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:05.353214+00:00\r\n\r\n'
2025-06-07 17:08:06,582 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:08:06,602 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 17:08:06,673 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 17:08:06,673 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 17:08:06,674 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 17:08:06,675 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 17:08:06,675 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 17:08:08,061 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 17:08:08,062 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i1.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_91b09fe3.jpg
2025-06-07 17:08:08,076 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 17:08:08,076 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:08.076894+00:00\r\n\r\n'
2025-06-07 17:08:08,078 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000275528AF1A0>
2025-06-07 17:08:08,079 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:08:08,080 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:08:08,080 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 17:08:08,080 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:08:08,080 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:08:08,081 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 17:08:08,081 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000275528A20D0> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 17:08:08,408 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000275528AF320>
2025-06-07 17:08:08,409 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 17:08:08,410 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:08:08,410 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 17:08:08,411 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:08:08,412 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 17:08:12,934 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:12.934017+00:00\r\n\r\n'
2025-06-07 17:08:12,934 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:12.934017+00:00\r\n\r\n'
2025-06-07 17:08:20,371 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:20.371413+00:00\r\n\r\n'
2025-06-07 17:08:23,101 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:23.101992+00:00\r\n\r\n'
2025-06-07 17:08:27,962 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:27.962772+00:00\r\n\r\n'
2025-06-07 17:08:27,964 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:27.963279+00:00\r\n\r\n'
2025-06-07 17:08:31,889 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 09:08:32 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2975'), (b'x-client-request-id', b'unknown-20250607170810-ECPieyDl'), (b'x-envoy-upstream-service-time', b'22991'), (b'x-request-id', b'021749287289599d566f247b8adf87a2e4b05873d7be22d6c5f27')])
2025-06-07 17:08:31,890 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 17:08:31,891 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 17:08:31,891 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 17:08:31,891 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 17:08:31,892 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 17:08:31,892 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 17:08:31,894 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:08:31,894 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=5, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 17:08:31,895 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":5,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 17:08:35,357 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:35.357144+00:00\r\n\r\n'
2025-06-07 17:08:38,108 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:38.108110+00:00\r\n\r\n'
2025-06-07 17:08:42,976 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:42.976209+00:00\r\n\r\n'
2025-06-07 17:08:42,976 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:42.976209+00:00\r\n\r\n'
2025-06-07 17:08:50,385 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:50.385931+00:00\r\n\r\n'
2025-06-07 17:08:53,121 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:53.121167+00:00\r\n\r\n'
2025-06-07 17:08:57,996 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:57.995401+00:00\r\n\r\n'
2025-06-07 17:08:57,997 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:08:57.997873+00:00\r\n\r\n'
2025-06-07 17:09:05,378 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:05.378519+00:00\r\n\r\n'
2025-06-07 17:09:08,100 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:08.100972+00:00\r\n\r\n'
2025-06-07 17:09:12,999 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:12.999103+00:00\r\n\r\n'
2025-06-07 17:09:13,000 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:13.000483+00:00\r\n\r\n'
2025-06-07 17:09:20,395 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:20.395487+00:00\r\n\r\n'
2025-06-07 17:09:23,111 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:23.111470+00:00\r\n\r\n'
2025-06-07 17:09:28,026 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:28.026709+00:00\r\n\r\n'
2025-06-07 17:09:28,027 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:28.027214+00:00\r\n\r\n'
2025-06-07 17:09:35,397 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:35.397562+00:00\r\n\r\n'
2025-06-07 17:09:38,113 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:09:38.113036+00:00\r\n\r\n'
2025-06-07 17:09:38,349 - root - DEBUG - [sse.py:159] - Client session disconnected 27e37772-4a8a-43ac-b7ac-aa0347612746
2025-06-07 17:09:38,349 - root - DEBUG - [sse.py:159] - Client session disconnected 17212680-fe8b-4fb5-b95b-9dcc6f71d45f
2025-06-07 17:09:38,349 - root - DEBUG - [sse.py:159] - Client session disconnected d7f3925a-42eb-4bea-9b92-47f637e6c1f2
2025-06-07 17:09:38,350 - root - DEBUG - [sse.py:159] - Client session disconnected d935b6cb-d6fe-4048-82ba-280c2545288f
2025-06-07 17:22:12,395 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 17:22:12,395 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 17:22:12,396 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 17:22:12,397 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 17:22:12,397 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 17:22:12,397 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 17:22:12,397 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 17:22:12,398 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 17:22:12,399 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 17:22:12,399 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8000/sse
2025-06-07 17:22:12,399 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 17:22:12,400 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 17:22:12,400 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 17:22:13,835 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 17:22:13,835 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 17:22:13,841 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 17:22:13,842 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 17:22:13,843 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8000/sse
2025-06-07 17:22:16,745 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:22:16,746 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: b469054e-39d3-4f6d-9ff5-a9b26e460fde
2025-06-07 17:22:16,746 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:22:16,747 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:22:16,750 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:22:16,750 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: c779ec31-d9ab-48dd-8123-45536a517e9f
2025-06-07 17:22:16,751 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:22:16,751 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:22:16,755 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:22:16,755 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 74a3a5c4-e735-445d-8f59-1b9ed0550b7f
2025-06-07 17:22:16,755 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:22:16,755 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:22:16,760 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:22:16,760 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:22:16,761 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: e13b342f-0e04-4314-9d78-b621a62b43af
2025-06-07 17:22:16,761 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:22:16,761 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:22:16,763 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=b469054e39d34f6d9ff5a9b26e460fde
2025-06-07 17:22:16,764 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:22:16,766 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:22:16,766 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=b469054e39d34f6d9ff5a9b26e460fde\r\n\r\n'
2025-06-07 17:22:16,767 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=c779ec31d9ab48dd812345536a517e9f
2025-06-07 17:22:16,767 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=74a3a5c4e735445d8f591b9ed0550b7f
2025-06-07 17:22:16,768 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:22:16,768 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=c779ec31d9ab48dd812345536a517e9f\r\n\r\n'
2025-06-07 17:22:16,768 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=74a3a5c4e735445d8f591b9ed0550b7f\r\n\r\n'
2025-06-07 17:22:16,769 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=e13b342f0e0443149d78b621a62b43af
2025-06-07 17:22:16,769 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=e13b342f0e0443149d78b621a62b43af\r\n\r\n'
2025-06-07 17:22:22,726 - root - DEBUG - [sse.py:159] - Client session disconnected b469054e-39d3-4f6d-9ff5-a9b26e460fde
2025-06-07 17:22:22,727 - root - DEBUG - [sse.py:159] - Client session disconnected c779ec31-d9ab-48dd-8123-45536a517e9f
2025-06-07 17:22:22,727 - root - DEBUG - [sse.py:159] - Client session disconnected 74a3a5c4-e735-445d-8f59-1b9ed0550b7f
2025-06-07 17:22:22,728 - root - DEBUG - [sse.py:159] - Client session disconnected e13b342f-0e04-4314-9d78-b621a62b43af
2025-06-07 17:36:46,416 - mcp.server.lowlevel.server - DEBUG - [server.py:150] - Initializing server 'Web Scraper with Vision Analysis'
2025-06-07 17:36:46,416 - mcp.server.lowlevel.server - DEBUG - [server.py:391] - Registering handler for ListToolsRequest
2025-06-07 17:36:46,417 - mcp.server.lowlevel.server - DEBUG - [server.py:413] - Registering handler for CallToolRequest
2025-06-07 17:36:46,417 - mcp.server.lowlevel.server - DEBUG - [server.py:259] - Registering handler for ListResourcesRequest
2025-06-07 17:36:46,417 - mcp.server.lowlevel.server - DEBUG - [server.py:293] - Registering handler for ReadResourceRequest
2025-06-07 17:36:46,418 - mcp.server.lowlevel.server - DEBUG - [server.py:229] - Registering handler for PromptListRequest
2025-06-07 17:36:46,418 - mcp.server.lowlevel.server - DEBUG - [server.py:246] - Registering handler for GetPromptRequest
2025-06-07 17:36:46,418 - mcp.server.lowlevel.server - DEBUG - [server.py:274] - Registering handler for ListResourceTemplatesRequest
2025-06-07 17:36:46,419 - __main__ - INFO - [main_clean.py:139] - 🚀 Starting Clean MCP Web Scraper Service...
2025-06-07 17:36:46,420 - __main__ - INFO - [main_clean.py:140] - 📍 Server URL: http://127.0.0.1:8000/sse
2025-06-07 17:36:46,420 - __main__ - INFO - [main_clean.py:141] - 📁 Output directory: scraped_data
2025-06-07 17:36:46,420 - __main__ - INFO - [main_clean.py:142] - 🤖 Doubao model: doubao-1-5-vision-pro-32k-250115
2025-06-07 17:36:46,422 - __main__ - INFO - [main_clean.py:147] - ✅ Output directory ready
2025-06-07 17:36:47,937 - __main__ - INFO - [main_clean.py:152] - ✅ Doubao API configuration loaded
2025-06-07 17:36:47,937 - __main__ - INFO - [main_clean.py:156] - 🎉 Starting server - ready to accept connections!
2025-06-07 17:36:47,944 - asyncio - DEBUG - [proactor_events.py:634] - Using proactor: IocpProactor
2025-06-07 17:36:47,946 - mcp.server.sse - DEBUG - [sse.py:84] - SseServerTransport initialized with endpoint: /messages/
2025-06-07 17:36:47,947 - FastMCP.fastmcp.server.server - INFO - [server.py:846] - Starting MCP server 'Web Scraper with Vision Analysis' with transport 'sse' on http://127.0.0.1:8000/sse
2025-06-07 17:36:49,469 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:36:49,469 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: c6d11272-c5be-4132-9869-326f9576e2b8
2025-06-07 17:36:49,470 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:36:49,470 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:36:49,473 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:36:49,474 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 1eb3bb86-8398-4b60-9377-f8349b82fb09
2025-06-07 17:36:49,474 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:36:49,474 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:36:49,478 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:36:49,478 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 6ab5f4e4-830f-4bec-8fd5-7b2b458aa5ff
2025-06-07 17:36:49,479 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:36:49,479 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:36:49,481 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:36:49,481 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 18105ba9-d6ff-45f5-a07d-d85133f3518f
2025-06-07 17:36:49,482 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:36:49,483 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:36:49,486 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:36:49,493 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=c6d11272c5be41329869326f9576e2b8
2025-06-07 17:36:49,495 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:36:49,496 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:36:49,497 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:36:49,497 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=c6d11272c5be41329869326f9576e2b8\r\n\r\n'
2025-06-07 17:36:49,497 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=1eb3bb8683984b609377f8349b82fb09
2025-06-07 17:36:49,498 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=6ab5f4e4830f4bec8fd57b2b458aa5ff
2025-06-07 17:36:49,501 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=18105ba9d6ff45f5a07dd85133f3518f
2025-06-07 17:36:49,501 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=1eb3bb8683984b609377f8349b82fb09\r\n\r\n'
2025-06-07 17:36:49,501 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=6ab5f4e4830f4bec8fd57b2b458aa5ff\r\n\r\n'
2025-06-07 17:36:49,502 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=18105ba9d6ff45f5a07dd85133f3518f\r\n\r\n'
2025-06-07 17:36:54,095 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:36:54,096 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: c6d11272-c5be-4132-9869-326f9576e2b8
2025-06-07 17:36:54,097 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":6}'
2025-06-07 17:36:54,097 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=6)
2025-06-07 17:36:54,097 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=6)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082870530>))
2025-06-07 17:36:54,099 - sse_starlette.sse - DEBUG - [sse.py:182] - Got event: http.disconnect. Stop streaming.
2025-06-07 17:36:57,107 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:36:57,107 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 0ac72ab1-1837-4887-bf5e-65fcab272dcc
2025-06-07 17:36:57,110 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:36:57,110 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:36:57,114 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:36:57,115 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=0ac72ab118374887bf5e65fcab272dcc
2025-06-07 17:36:57,115 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=0ac72ab118374887bf5e65fcab272dcc\r\n\r\n'
2025-06-07 17:37:04,509 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:04.509139+00:00\r\n\r\n'
2025-06-07 17:37:04,510 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:04.510141+00:00\r\n\r\n'
2025-06-07 17:37:04,511 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:04.511138+00:00\r\n\r\n'
2025-06-07 17:37:12,107 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:12.107583+00:00\r\n\r\n'
2025-06-07 17:37:19,509 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:19.509653+00:00\r\n\r\n'
2025-06-07 17:37:19,509 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:19.509653+00:00\r\n\r\n'
2025-06-07 17:37:19,511 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:19.511037+00:00\r\n\r\n'
2025-06-07 17:37:27,134 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:27.134073+00:00\r\n\r\n'
2025-06-07 17:37:34,517 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:34.517001+00:00\r\n\r\n'
2025-06-07 17:37:34,519 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:34.519004+00:00\r\n\r\n'
2025-06-07 17:37:34,519 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:34.519004+00:00\r\n\r\n'
2025-06-07 17:37:42,144 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:42.144907+00:00\r\n\r\n'
2025-06-07 17:37:49,527 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:49.527115+00:00\r\n\r\n'
2025-06-07 17:37:49,528 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:49.528333+00:00\r\n\r\n'
2025-06-07 17:37:49,529 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:49.529571+00:00\r\n\r\n'
2025-06-07 17:37:54,111 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:54,111 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 0ac72ab1-1837-4887-bf5e-65fcab272dcc
2025-06-07 17:37:54,112 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"jsonrpc":"2.0","method":"notifications/cancelled","params":{"requestId":6,"reason":"McpError: MCP error -32001: Request timed out"}}'
2025-06-07 17:37:54,113 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 6, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')
2025-06-07 17:37:54,113 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/cancelled', params={'requestId': 6, 'reason': 'McpError: MCP error -32001: Request timed out'}, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x00000280828733B0>))
2025-06-07 17:37:54,115 - mcp.server.sse - DEBUG - [sse.py:92] - Setting up SSE connection
2025-06-07 17:37:54,117 - mcp.server.sse - DEBUG - [sse.py:104] - Created new session with ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:54,117 - mcp.server.sse - DEBUG - [sse.py:161] - Starting SSE response task
2025-06-07 17:37:54,117 - mcp.server.sse - DEBUG - [sse.py:164] - Yielding read and write streams
2025-06-07 17:37:54,121 - mcp.server.sse - DEBUG - [sse.py:128] - Starting SSE writer
2025-06-07 17:37:54,121 - mcp.server.sse - DEBUG - [sse.py:133] - Sent endpoint event: /messages/?session_id=4781fabdde0a40188d77a4a5b76e9adb
2025-06-07 17:37:54,122 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: endpoint\r\ndata: /messages/?session_id=4781fabdde0a40188d77a4a5b76e9adb\r\n\r\n'
2025-06-07 17:37:54,124 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:54,125 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:54,126 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cherry Studio","version":"1.4.0"}},"jsonrpc":"2.0","id":0}'
2025-06-07 17:37:54,126 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)
2025-06-07 17:37:54,128 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'Cherry Studio', 'version': '1.4.0'}}, jsonrpc='2.0', id=0)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082919820>))
2025-06-07 17:37:54,132 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2025-03-26', 'capabilities': {'experimental': {}, 'prompts': {'listChanged': False}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': 'Web Scraper with Vision Analysis', 'version': '1.9.2'}})), metadata=None)
2025-06-07 17:37:54,133 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"Web Scraper with Vision Analysis","version":"1.9.2"}}}\r\n\r\n'
2025-06-07 17:37:54,134 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:54,135 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:54,135 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"notifications/initialized","jsonrpc":"2.0"}'
2025-06-07 17:37:54,135 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 17:37:54,137 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082919700>))
2025-06-07 17:37:54,138 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-06-07 17:37:54,140 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:54,140 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:54,140 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/list","jsonrpc":"2.0","id":1}'
2025-06-07 17:37:54,141 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)
2025-06-07 17:37:54,141 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002808291A570>))
2025-06-07 17:37:54,143 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000028082919FA0>
2025-06-07 17:37:54,144 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type ListToolsRequest
2025-06-07 17:37:54,144 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type ListToolsRequest
2025-06-07 17:37:54,145 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:37:54,145 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'scrape_and_analyze', 'description': '\n    一次性抓取网页并分析图片，返回完整的文本和图片解析数据\n    \n    Args:\n        url: 要抓取的网页URL\n        max_images: 最大分析图片数量，默认5张\n        \n    Returns:\n        包含网页文本、图片分析结果的完整数据\n    ', 'inputSchema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'max_images': {'default': 5, 'title': 'Max Images', 'type': 'integer'}}, 'required': ['url'], 'type': 'object'}}]})), metadata=None)
2025-06-07 17:37:54,145 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"scrape_and_analyze","description":"\\n    \xe4\xb8\x80\xe6\xac\xa1\xe6\x80\xa7\xe6\x8a\x93\xe5\x8f\x96\xe7\xbd\x91\xe9\xa1\xb5\xe5\xb9\xb6\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xef\xbc\x8c\xe8\xbf\x94\xe5\x9b\x9e\xe5\xae\x8c\xe6\x95\xb4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe5\x92\x8c\xe5\x9b\xbe\xe7\x89\x87\xe8\xa7\xa3\xe6\x9e\x90\xe6\x95\xb0\xe6\x8d\xae\\n    \\n    Args:\\n        url: \xe8\xa6\x81\xe6\x8a\x93\xe5\x8f\x96\xe7\x9a\x84\xe7\xbd\x91\xe9\xa1\xb5URL\\n        max_images: \xe6\x9c\x80\xe5\xa4\xa7\xe5\x88\x86\xe6\x9e\x90\xe5\x9b\xbe\xe7\x89\x87\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa45\xe5\xbc\xa0\\n        \\n    Returns:\\n        \xe5\x8c\x85\xe5\x90\xab\xe7\xbd\x91\xe9\xa1\xb5\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x81\xe5\x9b\xbe\xe7\x89\x87\xe5\x88\x86\xe6\x9e\x90\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe5\xae\x8c\xe6\x95\xb4\xe6\x95\xb0\xe6\x8d\xae\\n    ","inputSchema":{"properties":{"url":{"title":"Url","type":"string"},"max_images":{"default":5,"title":"Max Images","type":"integer"}},"required":["url"],"type":"object"}}]}}\r\n\r\n'
2025-06-07 17:37:57,023 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:57,023 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:57,024 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":2}'
2025-06-07 17:37:57,025 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)
2025-06-07 17:37:57,025 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=2)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x000002808291A990>))
2025-06-07 17:37:57,026 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x000002808291A5D0>
2025-06-07 17:37:57,026 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 17:37:57,026 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 17:37:57,027 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:37:57,027 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=2, result={})), metadata=None)
2025-06-07 17:37:57,027 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":2,"result":{}}\r\n\r\n'
2025-06-07 17:37:57,030 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:37:57,030 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:37:57,030 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"}},"jsonrpc":"2.0","id":3}'
2025-06-07 17:37:57,031 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)
2025-06-07 17:37:57,031 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=3)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082919700>))
2025-06-07 17:37:57,032 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x00000280827E8800>
2025-06-07 17:37:57,033 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 17:37:57,033 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 17:37:57,033 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:37:57,035 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 17:37:57,035 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 17:37:57,477 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:37:57.476359+00:00\r\n\r\n'
2025-06-07 17:38:01,304 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 17:38:01,828 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:38:04,551 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:04.551243+00:00\r\n\r\n'
2025-06-07 17:38:04,551 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:04.551243+00:00\r\n\r\n'
2025-06-07 17:38:04,553 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:04.553244+00:00\r\n\r\n'
2025-06-07 17:38:07,105 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:38:07,174 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 17:38:07,333 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 17:38:07,334 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 17:38:07,335 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 17:38:07,335 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 17:38:07,336 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 17:38:08,977 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 17:38:08,978 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i2.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_ee53b9df.jpg
2025-06-07 17:38:09,003 - PIL.Image - DEBUG - [Image.py:363] - Importing BlpImagePlugin
2025-06-07 17:38:09,006 - PIL.Image - DEBUG - [Image.py:363] - Importing BmpImagePlugin
2025-06-07 17:38:09,007 - PIL.Image - DEBUG - [Image.py:363] - Importing BufrStubImagePlugin
2025-06-07 17:38:09,008 - PIL.Image - DEBUG - [Image.py:363] - Importing CurImagePlugin
2025-06-07 17:38:09,009 - PIL.Image - DEBUG - [Image.py:363] - Importing DcxImagePlugin
2025-06-07 17:38:09,012 - PIL.Image - DEBUG - [Image.py:363] - Importing DdsImagePlugin
2025-06-07 17:38:09,016 - PIL.Image - DEBUG - [Image.py:363] - Importing EpsImagePlugin
2025-06-07 17:38:09,018 - PIL.Image - DEBUG - [Image.py:363] - Importing FitsImagePlugin
2025-06-07 17:38:09,019 - PIL.Image - DEBUG - [Image.py:363] - Importing FliImagePlugin
2025-06-07 17:38:09,020 - PIL.Image - DEBUG - [Image.py:363] - Importing FpxImagePlugin
2025-06-07 17:38:09,023 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import FpxImagePlugin: No module named 'olefile'
2025-06-07 17:38:09,023 - PIL.Image - DEBUG - [Image.py:363] - Importing FtexImagePlugin
2025-06-07 17:38:09,025 - PIL.Image - DEBUG - [Image.py:363] - Importing GbrImagePlugin
2025-06-07 17:38:09,026 - PIL.Image - DEBUG - [Image.py:363] - Importing GifImagePlugin
2025-06-07 17:38:09,026 - PIL.Image - DEBUG - [Image.py:363] - Importing GribStubImagePlugin
2025-06-07 17:38:09,028 - PIL.Image - DEBUG - [Image.py:363] - Importing Hdf5StubImagePlugin
2025-06-07 17:38:09,029 - PIL.Image - DEBUG - [Image.py:363] - Importing IcnsImagePlugin
2025-06-07 17:38:09,032 - PIL.Image - DEBUG - [Image.py:363] - Importing IcoImagePlugin
2025-06-07 17:38:09,035 - PIL.Image - DEBUG - [Image.py:363] - Importing ImImagePlugin
2025-06-07 17:38:09,036 - PIL.Image - DEBUG - [Image.py:363] - Importing ImtImagePlugin
2025-06-07 17:38:09,038 - PIL.Image - DEBUG - [Image.py:363] - Importing IptcImagePlugin
2025-06-07 17:38:09,039 - PIL.Image - DEBUG - [Image.py:363] - Importing JpegImagePlugin
2025-06-07 17:38:09,039 - PIL.Image - DEBUG - [Image.py:363] - Importing Jpeg2KImagePlugin
2025-06-07 17:38:09,040 - PIL.Image - DEBUG - [Image.py:363] - Importing McIdasImagePlugin
2025-06-07 17:38:09,043 - PIL.Image - DEBUG - [Image.py:363] - Importing MicImagePlugin
2025-06-07 17:38:09,047 - PIL.Image - DEBUG - [Image.py:366] - Image: failed to import MicImagePlugin: No module named 'olefile'
2025-06-07 17:38:09,047 - PIL.Image - DEBUG - [Image.py:363] - Importing MpegImagePlugin
2025-06-07 17:38:09,048 - PIL.Image - DEBUG - [Image.py:363] - Importing MpoImagePlugin
2025-06-07 17:38:09,051 - PIL.Image - DEBUG - [Image.py:363] - Importing MspImagePlugin
2025-06-07 17:38:09,053 - PIL.Image - DEBUG - [Image.py:363] - Importing PalmImagePlugin
2025-06-07 17:38:09,054 - PIL.Image - DEBUG - [Image.py:363] - Importing PcdImagePlugin
2025-06-07 17:38:09,055 - PIL.Image - DEBUG - [Image.py:363] - Importing PcxImagePlugin
2025-06-07 17:38:09,056 - PIL.Image - DEBUG - [Image.py:363] - Importing PdfImagePlugin
2025-06-07 17:38:09,061 - PIL.Image - DEBUG - [Image.py:363] - Importing PixarImagePlugin
2025-06-07 17:38:09,063 - PIL.Image - DEBUG - [Image.py:363] - Importing PngImagePlugin
2025-06-07 17:38:09,063 - PIL.Image - DEBUG - [Image.py:363] - Importing PpmImagePlugin
2025-06-07 17:38:09,064 - PIL.Image - DEBUG - [Image.py:363] - Importing PsdImagePlugin
2025-06-07 17:38:09,065 - PIL.Image - DEBUG - [Image.py:363] - Importing QoiImagePlugin
2025-06-07 17:38:09,066 - PIL.Image - DEBUG - [Image.py:363] - Importing SgiImagePlugin
2025-06-07 17:38:09,067 - PIL.Image - DEBUG - [Image.py:363] - Importing SpiderImagePlugin
2025-06-07 17:38:09,068 - PIL.Image - DEBUG - [Image.py:363] - Importing SunImagePlugin
2025-06-07 17:38:09,070 - PIL.Image - DEBUG - [Image.py:363] - Importing TgaImagePlugin
2025-06-07 17:38:09,071 - PIL.Image - DEBUG - [Image.py:363] - Importing TiffImagePlugin
2025-06-07 17:38:09,072 - PIL.Image - DEBUG - [Image.py:363] - Importing WebPImagePlugin
2025-06-07 17:38:09,074 - PIL.Image - DEBUG - [Image.py:363] - Importing WmfImagePlugin
2025-06-07 17:38:09,076 - PIL.Image - DEBUG - [Image.py:363] - Importing XbmImagePlugin
2025-06-07 17:38:09,077 - PIL.Image - DEBUG - [Image.py:363] - Importing XpmImagePlugin
2025-06-07 17:38:09,079 - PIL.Image - DEBUG - [Image.py:363] - Importing XVThumbImagePlugin
2025-06-07 17:38:09,094 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 17:38:09,095 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:09.095633+00:00\r\n\r\n'
2025-06-07 17:38:09,096 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000028082DD0470>
2025-06-07 17:38:09,097 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:38:09,097 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:38:09,097 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 17:38:09,098 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:38:09,098 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:38:09,098 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 17:38:09,098 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000028082CC08D0> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 17:38:09,190 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000028082DD0410>
2025-06-07 17:38:09,190 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 17:38:09,192 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:38:09,192 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 17:38:09,193 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:38:09,193 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 17:38:12,494 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:12.494903+00:00\r\n\r\n'
2025-06-07 17:38:19,544 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:19.544592+00:00\r\n\r\n'
2025-06-07 17:38:19,545 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:19.545097+00:00\r\n\r\n'
2025-06-07 17:38:19,546 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:19.546112+00:00\r\n\r\n'
2025-06-07 17:38:24,118 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:24.118548+00:00\r\n\r\n'
2025-06-07 17:38:27,499 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:27.499053+00:00\r\n\r\n'
2025-06-07 17:38:28,641 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 09:38:29 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2928'), (b'x-client-request-id', b'unknown-20250607173810-yzMdFRFs'), (b'x-envoy-upstream-service-time', b'19173'), (b'x-request-id', b'021749289090355fb06bdf8115f8c970512482062132191fe40f3')])
2025-06-07 17:38:28,642 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 17:38:28,642 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 17:38:28,643 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 17:38:28,643 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 17:38:28,643 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 17:38:28,644 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 17:38:28,647 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:38:28,648 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=3, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 17:38:28,648 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":3,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 17:38:33,646 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:38:33,646 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:38:33,647 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"ping","jsonrpc":"2.0","id":4}'
2025-06-07 17:38:33,647 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)
2025-06-07 17:38:33,647 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='ping', params=None, jsonrpc='2.0', id=4)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082C825D0>))
2025-06-07 17:38:33,649 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000028082919E20>
2025-06-07 17:38:33,650 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type PingRequest
2025-06-07 17:38:33,650 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type PingRequest
2025-06-07 17:38:33,651 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:38:33,651 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=4, result={})), metadata=None)
2025-06-07 17:38:33,651 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":4,"result":{}}\r\n\r\n'
2025-06-07 17:38:33,654 - mcp.server.sse - DEBUG - [sse.py:170] - Handling POST message
2025-06-07 17:38:33,654 - mcp.server.sse - DEBUG - [sse.py:181] - Parsed session ID: 4781fabd-de0a-4018-8d77-a4a5b76e9adb
2025-06-07 17:38:33,654 - mcp.server.sse - DEBUG - [sse.py:194] - Received JSON: b'{"method":"tools/call","params":{"name":"scrape_and_analyze","arguments":{"url":"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc"}},"jsonrpc":"2.0","id":5}'
2025-06-07 17:38:33,655 - mcp.server.sse - DEBUG - [sse.py:198] - Validated client message: root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=5)
2025-06-07 17:38:33,655 - mcp.server.sse - DEBUG - [sse.py:209] - Sending session message to writer: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/call', params={'name': 'scrape_and_analyze', 'arguments': {'url': 'https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc'}}, jsonrpc='2.0', id=5)), metadata=ServerMessageMetadata(related_request_id=None, request_context=<starlette.requests.Request object at 0x0000028082C835F0>))
2025-06-07 17:38:33,656 - mcp.server.lowlevel.server - DEBUG - [server.py:513] - Received message: <mcp.shared.session.RequestResponder object at 0x0000028082609AC0>
2025-06-07 17:38:33,658 - mcp.server.lowlevel.server - INFO - [server.py:556] - Processing request of type CallToolRequest
2025-06-07 17:38:33,658 - mcp.server.lowlevel.server - DEBUG - [server.py:559] - Dispatching request of type CallToolRequest
2025-06-07 17:38:33,658 - __main__ - INFO - [main_clean.py:54] - 🚀 开始抓取和分析: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:38:33,658 - __main__ - DEBUG - [main_clean.py:55] - 参数: max_images=5
2025-06-07 17:38:33,659 - __main__ - INFO - [main_clean.py:58] - 📄 开始抓取网页内容...
2025-06-07 17:38:34,571 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:34.571762+00:00\r\n\r\n'
2025-06-07 17:38:34,571 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:34.571762+00:00\r\n\r\n'
2025-06-07 17:38:34,573 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:34.573104+00:00\r\n\r\n'
2025-06-07 17:38:37,667 - web_scraper - INFO - [web_scraper.py:55] - Browser started successfully
2025-06-07 17:38:38,132 - web_scraper - INFO - [web_scraper.py:226] - Navigating to: https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:38:39,114 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:39.114455+00:00\r\n\r\n'
2025-06-07 17:38:42,509 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:42.509834+00:00\r\n\r\n'
2025-06-07 17:38:43,285 - web_scraper - INFO - [web_scraper.py:196] - Downloaded 10 images from https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc
2025-06-07 17:38:43,343 - web_scraper - INFO - [web_scraper.py:299] - Successfully scraped https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 424 chars, 10 images
2025-06-07 17:38:43,476 - web_scraper - INFO - [web_scraper.py:67] - Browser closed successfully
2025-06-07 17:38:43,476 - __main__ - INFO - [main_clean.py:70] - ✅ 网页抓取成功 - 标题: 商品详情
2025-06-07 17:38:43,477 - __main__ - INFO - [main_clean.py:71] - 📊 文本长度: 424 字符
2025-06-07 17:38:43,478 - __main__ - INFO - [main_clean.py:72] - 🖼️ 发现图片: 10 张
2025-06-07 17:38:43,478 - __main__ - INFO - [main_clean.py:77] - 🔍 开始分析图片...
2025-06-07 17:38:45,120 - __main__ - INFO - [main_clean.py:82] - 📸 将分析 5 张图片 (共 10 张)
2025-06-07 17:38:45,120 - __main__ - DEBUG - [main_clean.py:86] - 分析第 1 张图片: mall-i2.xhscdn.com_arkgoods_1040g3no31fg9inqgn00g5n4kjj9k0au0ogfdg38_ee53b9df.jpg
2025-06-07 17:38:45,136 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=10808 local_address=None timeout=60.0 socket_options=None
2025-06-07 17:38:45,137 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000028082E8CE30>
2025-06-07 17:38:45,138 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:38:45,139 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:38:45,139 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-06-07 17:38:45,139 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:38:45,139 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-06-07 17:38:45,140 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-06-07 17:38:45,140 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000028082E82B50> server_hostname='ark.cn-beijing.volces.com' timeout=60.0
2025-06-07 17:38:45,258 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000028082E8CF80>
2025-06-07 17:38:45,259 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-06-07 17:38:45,260 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-06-07 17:38:45,260 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-06-07 17:38:45,261 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-06-07 17:38:45,261 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-06-07 17:38:49,576 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:49.576985+00:00\r\n\r\n'
2025-06-07 17:38:49,576 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:49.576985+00:00\r\n\r\n'
2025-06-07 17:38:49,577 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:49.577987+00:00\r\n\r\n'
2025-06-07 17:38:54,121 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:54.121287+00:00\r\n\r\n'
2025-06-07 17:38:57,519 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:38:57.519362+00:00\r\n\r\n'
2025-06-07 17:39:03,895 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'server', b'istio-envoy'), (b'date', b'Sat, 07 Jun 2025 09:39:04 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-length', b'2808'), (b'x-client-request-id', b'unknown-20250607173846-wwvxhTVR'), (b'x-envoy-upstream-service-time', b'18339'), (b'x-request-id', b'02174928912641468617fd44bbb008a3b8038ac488c9ef6920d61')])
2025-06-07 17:39:03,896 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-06-07 17:39:03,897 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-06-07 17:39:03,898 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-06-07 17:39:03,898 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-06-07 17:39:03,898 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-06-07 17:39:03,899 - __main__ - ERROR - [main_clean.py:131] - 💥 抓取分析错误 https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc: 'url'
Traceback (most recent call last):
  File "D:\workspace\gitee.com\coffee\mcp\main_clean.py", line 97, in scrape_and_analyze
    'url': img_info['url'],
           ~~~~~~~~^^^^^^^
KeyError: 'url'
2025-06-07 17:39:03,901 - mcp.server.lowlevel.server - DEBUG - [server.py:602] - Response sent
2025-06-07 17:39:03,901 - mcp.server.sse - DEBUG - [sse.py:136] - Sending message via SSE: SessionMessage(message=JSONRPCMessage(root=JSONRPCResponse(jsonrpc='2.0', id=5, result={'content': [{'type': 'text', 'text': '{\n  "success": false,\n  "url": "https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc",\n  "error": "\'url\'"\n}'}], 'isError': False})), metadata=None)
2025-06-07 17:39:03,902 - sse_starlette.sse - DEBUG - [sse.py:161] - chunk: b'event: message\r\ndata: {"jsonrpc":"2.0","id":5,"result":{"content":[{"type":"text","text":"{\\n  \\"success\\": false,\\n  \\"url\\": \\"https://www.xiaohongshu.com/goods-detail/67d656348599cb00017adecc\\",\\n  \\"error\\": \\"\'url\'\\"\\n}"}],"isError":false}}\r\n\r\n'
2025-06-07 17:39:04,600 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:04.600658+00:00\r\n\r\n'
2025-06-07 17:39:04,600 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:04.600658+00:00\r\n\r\n'
2025-06-07 17:39:04,601 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:04.601662+00:00\r\n\r\n'
2025-06-07 17:39:09,147 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:09.147494+00:00\r\n\r\n'
2025-06-07 17:39:12,525 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:12.525715+00:00\r\n\r\n'
2025-06-07 17:39:19,609 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:19.609307+00:00\r\n\r\n'
2025-06-07 17:39:19,610 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:19.609307+00:00\r\n\r\n'
2025-06-07 17:39:19,610 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:19.610310+00:00\r\n\r\n'
2025-06-07 17:39:24,158 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:24.158366+00:00\r\n\r\n'
2025-06-07 17:39:27,528 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:27.528121+00:00\r\n\r\n'
2025-06-07 17:39:34,612 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:34.612556+00:00\r\n\r\n'
2025-06-07 17:39:34,612 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:34.612556+00:00\r\n\r\n'
2025-06-07 17:39:34,613 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:34.613559+00:00\r\n\r\n'
2025-06-07 17:39:39,183 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:39.183830+00:00\r\n\r\n'
2025-06-07 17:39:42,542 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:42.542750+00:00\r\n\r\n'
2025-06-07 17:39:49,631 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:49.631948+00:00\r\n\r\n'
2025-06-07 17:39:49,631 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:49.631948+00:00\r\n\r\n'
2025-06-07 17:39:49,633 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:49.633199+00:00\r\n\r\n'
2025-06-07 17:39:54,168 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:54.168987+00:00\r\n\r\n'
2025-06-07 17:39:57,563 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:39:57.563817+00:00\r\n\r\n'
2025-06-07 17:40:04,633 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:04.633615+00:00\r\n\r\n'
2025-06-07 17:40:04,633 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:04.633615+00:00\r\n\r\n'
2025-06-07 17:40:04,634 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:04.634790+00:00\r\n\r\n'
2025-06-07 17:40:09,171 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:09.171158+00:00\r\n\r\n'
2025-06-07 17:40:12,582 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:12.582443+00:00\r\n\r\n'
2025-06-07 17:40:19,636 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:19.636051+00:00\r\n\r\n'
2025-06-07 17:40:19,636 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:19.636571+00:00\r\n\r\n'
2025-06-07 17:40:19,637 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:19.637101+00:00\r\n\r\n'
2025-06-07 17:40:24,170 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:24.170516+00:00\r\n\r\n'
2025-06-07 17:40:27,593 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:27.593086+00:00\r\n\r\n'
2025-06-07 17:40:34,664 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:34.664619+00:00\r\n\r\n'
2025-06-07 17:40:34,665 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:34.665125+00:00\r\n\r\n'
2025-06-07 17:40:34,666 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:34.666130+00:00\r\n\r\n'
2025-06-07 17:40:39,189 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:39.189975+00:00\r\n\r\n'
2025-06-07 17:40:42,612 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:42.612489+00:00\r\n\r\n'
2025-06-07 17:40:49,665 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:49.665629+00:00\r\n\r\n'
2025-06-07 17:40:49,665 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:49.665629+00:00\r\n\r\n'
2025-06-07 17:40:49,666 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:49.666965+00:00\r\n\r\n'
2025-06-07 17:40:54,196 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:54.196209+00:00\r\n\r\n'
2025-06-07 17:40:57,605 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:40:57.605517+00:00\r\n\r\n'
2025-06-07 17:41:04,647 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:04.647620+00:00\r\n\r\n'
2025-06-07 17:41:04,648 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:04.648126+00:00\r\n\r\n'
2025-06-07 17:41:04,649 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:04.649209+00:00\r\n\r\n'
2025-06-07 17:41:09,193 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:09.193102+00:00\r\n\r\n'
2025-06-07 17:41:12,636 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:12.636213+00:00\r\n\r\n'
2025-06-07 17:41:19,661 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:19.661498+00:00\r\n\r\n'
2025-06-07 17:41:19,661 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:19.661498+00:00\r\n\r\n'
2025-06-07 17:41:19,662 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:19.662495+00:00\r\n\r\n'
2025-06-07 17:41:24,180 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:24.180149+00:00\r\n\r\n'
2025-06-07 17:41:27,642 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:27.642760+00:00\r\n\r\n'
2025-06-07 17:41:34,684 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:34.684155+00:00\r\n\r\n'
2025-06-07 17:41:34,684 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:34.684155+00:00\r\n\r\n'
2025-06-07 17:41:34,685 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:34.685306+00:00\r\n\r\n'
2025-06-07 17:41:39,197 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:39.197371+00:00\r\n\r\n'
2025-06-07 17:41:42,658 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:42.658756+00:00\r\n\r\n'
2025-06-07 17:41:49,674 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:49.674012+00:00\r\n\r\n'
2025-06-07 17:41:49,674 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:49.674012+00:00\r\n\r\n'
2025-06-07 17:41:49,675 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:49.675516+00:00\r\n\r\n'
2025-06-07 17:41:54,202 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:54.202348+00:00\r\n\r\n'
2025-06-07 17:41:57,675 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:41:57.675661+00:00\r\n\r\n'
2025-06-07 17:42:04,695 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:04.695268+00:00\r\n\r\n'
2025-06-07 17:42:04,695 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:04.695268+00:00\r\n\r\n'
2025-06-07 17:42:04,696 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:04.696415+00:00\r\n\r\n'
2025-06-07 17:42:09,230 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:09.230372+00:00\r\n\r\n'
2025-06-07 17:42:12,669 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:12.669222+00:00\r\n\r\n'
2025-06-07 17:42:19,709 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:19.709406+00:00\r\n\r\n'
2025-06-07 17:42:19,709 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:19.709406+00:00\r\n\r\n'
2025-06-07 17:42:19,710 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:19.710591+00:00\r\n\r\n'
2025-06-07 17:42:24,229 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:24.229122+00:00\r\n\r\n'
2025-06-07 17:42:27,692 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:27.692352+00:00\r\n\r\n'
2025-06-07 17:42:34,726 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:34.726321+00:00\r\n\r\n'
2025-06-07 17:42:34,726 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:34.726321+00:00\r\n\r\n'
2025-06-07 17:42:34,727 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:34.727579+00:00\r\n\r\n'
2025-06-07 17:42:39,242 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:39.242503+00:00\r\n\r\n'
2025-06-07 17:42:42,695 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:42.695804+00:00\r\n\r\n'
2025-06-07 17:42:49,732 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:49.732552+00:00\r\n\r\n'
2025-06-07 17:42:49,732 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:49.732552+00:00\r\n\r\n'
2025-06-07 17:42:49,734 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:49.734266+00:00\r\n\r\n'
2025-06-07 17:42:54,258 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:54.258973+00:00\r\n\r\n'
2025-06-07 17:42:57,715 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:42:57.715454+00:00\r\n\r\n'
2025-06-07 17:43:04,750 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:04.750494+00:00\r\n\r\n'
2025-06-07 17:43:04,751 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:04.751680+00:00\r\n\r\n'
2025-06-07 17:43:04,752 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:04.752720+00:00\r\n\r\n'
2025-06-07 17:43:09,248 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:09.248564+00:00\r\n\r\n'
2025-06-07 17:43:12,724 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:12.724146+00:00\r\n\r\n'
2025-06-07 17:43:19,765 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:19.765170+00:00\r\n\r\n'
2025-06-07 17:43:19,765 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:19.765170+00:00\r\n\r\n'
2025-06-07 17:43:19,766 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:19.766174+00:00\r\n\r\n'
2025-06-07 17:43:24,267 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:24.267857+00:00\r\n\r\n'
2025-06-07 17:43:27,732 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:27.732053+00:00\r\n\r\n'
2025-06-07 17:43:34,778 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:34.778296+00:00\r\n\r\n'
2025-06-07 17:43:34,778 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:34.778296+00:00\r\n\r\n'
2025-06-07 17:43:34,779 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:34.779430+00:00\r\n\r\n'
2025-06-07 17:43:39,284 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:39.284261+00:00\r\n\r\n'
2025-06-07 17:43:42,756 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:42.756366+00:00\r\n\r\n'
2025-06-07 17:43:49,796 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:49.796344+00:00\r\n\r\n'
2025-06-07 17:43:49,796 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:49.796344+00:00\r\n\r\n'
2025-06-07 17:43:49,798 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:49.798350+00:00\r\n\r\n'
2025-06-07 17:43:54,301 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:54.301762+00:00\r\n\r\n'
2025-06-07 17:43:57,769 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:43:57.769931+00:00\r\n\r\n'
2025-06-07 17:44:04,818 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:04.818369+00:00\r\n\r\n'
2025-06-07 17:44:04,819 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:04.818369+00:00\r\n\r\n'
2025-06-07 17:44:04,819 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:04.819371+00:00\r\n\r\n'
2025-06-07 17:44:09,304 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:09.304348+00:00\r\n\r\n'
2025-06-07 17:44:12,768 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:12.768967+00:00\r\n\r\n'
2025-06-07 17:44:19,804 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:19.804583+00:00\r\n\r\n'
2025-06-07 17:44:19,805 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:19.805584+00:00\r\n\r\n'
2025-06-07 17:44:19,805 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:19.805584+00:00\r\n\r\n'
2025-06-07 17:44:24,304 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:24.304402+00:00\r\n\r\n'
2025-06-07 17:44:27,778 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:27.778315+00:00\r\n\r\n'
2025-06-07 17:44:34,815 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:34.815392+00:00\r\n\r\n'
2025-06-07 17:44:34,815 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:34.815392+00:00\r\n\r\n'
2025-06-07 17:44:34,816 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:34.816740+00:00\r\n\r\n'
2025-06-07 17:44:39,295 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:39.295765+00:00\r\n\r\n'
2025-06-07 17:44:42,762 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:42.762646+00:00\r\n\r\n'
2025-06-07 17:44:49,809 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:49.809688+00:00\r\n\r\n'
2025-06-07 17:44:49,811 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:49.811005+00:00\r\n\r\n'
2025-06-07 17:44:49,811 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:49.811005+00:00\r\n\r\n'
2025-06-07 17:44:54,321 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:54.321004+00:00\r\n\r\n'
2025-06-07 17:44:57,780 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:44:57.780732+00:00\r\n\r\n'
2025-06-07 17:45:04,836 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:04.836763+00:00\r\n\r\n'
2025-06-07 17:45:04,836 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:04.836763+00:00\r\n\r\n'
2025-06-07 17:45:04,837 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:04.837973+00:00\r\n\r\n'
2025-06-07 17:45:09,322 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:09.322044+00:00\r\n\r\n'
2025-06-07 17:45:12,800 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:12.800349+00:00\r\n\r\n'
2025-06-07 17:45:19,830 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:19.830143+00:00\r\n\r\n'
2025-06-07 17:45:19,831 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:19.831147+00:00\r\n\r\n'
2025-06-07 17:45:19,832 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:19.832143+00:00\r\n\r\n'
2025-06-07 17:45:24,334 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:24.334001+00:00\r\n\r\n'
2025-06-07 17:45:27,819 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:27.819418+00:00\r\n\r\n'
2025-06-07 17:45:34,842 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:34.842728+00:00\r\n\r\n'
2025-06-07 17:45:34,842 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:34.842728+00:00\r\n\r\n'
2025-06-07 17:45:34,843 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:34.843731+00:00\r\n\r\n'
2025-06-07 17:45:39,351 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:39.351442+00:00\r\n\r\n'
2025-06-07 17:45:42,822 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:42.822366+00:00\r\n\r\n'
2025-06-07 17:45:49,868 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:49.868364+00:00\r\n\r\n'
2025-06-07 17:45:49,869 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:49.869367+00:00\r\n\r\n'
2025-06-07 17:45:49,870 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:49.870365+00:00\r\n\r\n'
2025-06-07 17:45:54,371 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:54.371010+00:00\r\n\r\n'
2025-06-07 17:45:57,838 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:45:57.838779+00:00\r\n\r\n'
2025-06-07 17:46:04,858 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:04.858982+00:00\r\n\r\n'
2025-06-07 17:46:04,858 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:04.858982+00:00\r\n\r\n'
2025-06-07 17:46:04,860 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:04.860305+00:00\r\n\r\n'
2025-06-07 17:46:09,397 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:09.397680+00:00\r\n\r\n'
2025-06-07 17:46:12,850 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:12.850969+00:00\r\n\r\n'
2025-06-07 17:46:19,873 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:19.873790+00:00\r\n\r\n'
2025-06-07 17:46:19,873 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:19.873790+00:00\r\n\r\n'
2025-06-07 17:46:19,874 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:19.874794+00:00\r\n\r\n'
2025-06-07 17:46:24,418 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:24.418137+00:00\r\n\r\n'
2025-06-07 17:46:27,870 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:27.870817+00:00\r\n\r\n'
2025-06-07 17:46:34,874 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:34.874940+00:00\r\n\r\n'
2025-06-07 17:46:34,875 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:34.875494+00:00\r\n\r\n'
2025-06-07 17:46:34,876 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:34.876500+00:00\r\n\r\n'
2025-06-07 17:46:39,425 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:39.425971+00:00\r\n\r\n'
2025-06-07 17:46:42,871 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:42.871802+00:00\r\n\r\n'
2025-06-07 17:46:49,887 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:49.887350+00:00\r\n\r\n'
2025-06-07 17:46:49,887 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:49.887350+00:00\r\n\r\n'
2025-06-07 17:46:49,888 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:49.888349+00:00\r\n\r\n'
2025-06-07 17:46:54,427 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:54.427123+00:00\r\n\r\n'
2025-06-07 17:46:57,859 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:46:57.859186+00:00\r\n\r\n'
2025-06-07 17:47:06,021 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:06.021414+00:00\r\n\r\n'
2025-06-07 17:47:06,022 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:06.021414+00:00\r\n\r\n'
2025-06-07 17:47:06,022 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:06.022422+00:00\r\n\r\n'
2025-06-07 17:47:10,543 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:10.543224+00:00\r\n\r\n'
2025-06-07 17:47:13,982 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:13.982332+00:00\r\n\r\n'
2025-06-07 17:47:21,036 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:21.036701+00:00\r\n\r\n'
2025-06-07 17:47:21,037 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:21.037207+00:00\r\n\r\n'
2025-06-07 17:47:21,038 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:21.038212+00:00\r\n\r\n'
2025-06-07 17:47:25,542 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:25.542209+00:00\r\n\r\n'
2025-06-07 17:47:29,003 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:29.003728+00:00\r\n\r\n'
2025-06-07 17:47:36,045 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:36.045452+00:00\r\n\r\n'
2025-06-07 17:47:36,045 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:36.045452+00:00\r\n\r\n'
2025-06-07 17:47:36,046 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:36.046454+00:00\r\n\r\n'
2025-06-07 17:47:40,534 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:40.534168+00:00\r\n\r\n'
2025-06-07 17:47:44,019 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:44.019854+00:00\r\n\r\n'
2025-06-07 17:47:51,059 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:51.059653+00:00\r\n\r\n'
2025-06-07 17:47:51,061 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:51.059653+00:00\r\n\r\n'
2025-06-07 17:47:51,062 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:51.062137+00:00\r\n\r\n'
2025-06-07 17:47:55,543 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:55.543374+00:00\r\n\r\n'
2025-06-07 17:47:59,008 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:47:59.008655+00:00\r\n\r\n'
2025-06-07 17:48:06,067 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:06.067058+00:00\r\n\r\n'
2025-06-07 17:48:06,067 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:06.067058+00:00\r\n\r\n'
2025-06-07 17:48:06,068 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:06.068189+00:00\r\n\r\n'
2025-06-07 17:48:10,554 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:10.554752+00:00\r\n\r\n'
2025-06-07 17:48:14,021 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:14.021332+00:00\r\n\r\n'
2025-06-07 17:48:21,078 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:21.078270+00:00\r\n\r\n'
2025-06-07 17:48:21,078 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:21.078270+00:00\r\n\r\n'
2025-06-07 17:48:21,078 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:21.078270+00:00\r\n\r\n'
2025-06-07 17:48:25,563 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:25.563565+00:00\r\n\r\n'
2025-06-07 17:48:29,029 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:29.029131+00:00\r\n\r\n'
2025-06-07 17:48:36,094 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:36.094067+00:00\r\n\r\n'
2025-06-07 17:48:36,094 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:36.094067+00:00\r\n\r\n'
2025-06-07 17:48:36,096 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:36.096494+00:00\r\n\r\n'
2025-06-07 17:48:40,583 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:40.583094+00:00\r\n\r\n'
2025-06-07 17:48:44,053 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:44.053523+00:00\r\n\r\n'
2025-06-07 17:48:51,079 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:51.079873+00:00\r\n\r\n'
2025-06-07 17:48:51,079 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:51.079873+00:00\r\n\r\n'
2025-06-07 17:48:51,081 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:51.081015+00:00\r\n\r\n'
2025-06-07 17:48:55,597 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:55.597514+00:00\r\n\r\n'
2025-06-07 17:48:59,069 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:48:59.069082+00:00\r\n\r\n'
2025-06-07 17:49:06,102 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:06.102607+00:00\r\n\r\n'
2025-06-07 17:49:06,102 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:06.102607+00:00\r\n\r\n'
2025-06-07 17:49:06,104 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:06.104623+00:00\r\n\r\n'
2025-06-07 17:49:10,599 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:10.599475+00:00\r\n\r\n'
2025-06-07 17:49:14,069 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:14.069461+00:00\r\n\r\n'
2025-06-07 17:49:21,115 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:21.115133+00:00\r\n\r\n'
2025-06-07 17:49:21,115 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:21.115133+00:00\r\n\r\n'
2025-06-07 17:49:21,116 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:21.116452+00:00\r\n\r\n'
2025-06-07 17:49:25,583 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:25.583362+00:00\r\n\r\n'
2025-06-07 17:49:29,080 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:29.080261+00:00\r\n\r\n'
2025-06-07 17:49:36,125 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:36.125511+00:00\r\n\r\n'
2025-06-07 17:49:36,125 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:36.125511+00:00\r\n\r\n'
2025-06-07 17:49:36,126 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:36.126520+00:00\r\n\r\n'
2025-06-07 17:49:40,592 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:40.592904+00:00\r\n\r\n'
2025-06-07 17:49:44,090 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:44.090598+00:00\r\n\r\n'
2025-06-07 17:49:51,151 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:51.151954+00:00\r\n\r\n'
2025-06-07 17:49:51,153 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:51.153266+00:00\r\n\r\n'
2025-06-07 17:49:51,154 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:51.154448+00:00\r\n\r\n'
2025-06-07 17:49:55,585 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:55.585320+00:00\r\n\r\n'
2025-06-07 17:49:59,091 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:49:59.091895+00:00\r\n\r\n'
2025-06-07 17:50:06,173 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:06.173419+00:00\r\n\r\n'
2025-06-07 17:50:06,174 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:06.174423+00:00\r\n\r\n'
2025-06-07 17:50:06,175 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:06.175420+00:00\r\n\r\n'
2025-06-07 17:50:10,593 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:10.593006+00:00\r\n\r\n'
2025-06-07 17:50:14,117 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:14.117837+00:00\r\n\r\n'
2025-06-07 17:50:21,168 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:21.168706+00:00\r\n\r\n'
2025-06-07 17:50:21,168 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:21.168706+00:00\r\n\r\n'
2025-06-07 17:50:21,170 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:21.170006+00:00\r\n\r\n'
2025-06-07 17:50:25,598 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:25.598030+00:00\r\n\r\n'
2025-06-07 17:50:29,129 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:29.129743+00:00\r\n\r\n'
2025-06-07 17:50:36,190 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:36.190877+00:00\r\n\r\n'
2025-06-07 17:50:36,190 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:36.190877+00:00\r\n\r\n'
2025-06-07 17:50:36,192 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:36.192058+00:00\r\n\r\n'
2025-06-07 17:50:40,605 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:40.605104+00:00\r\n\r\n'
2025-06-07 17:50:44,141 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:44.141289+00:00\r\n\r\n'
2025-06-07 17:50:51,215 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:51.215989+00:00\r\n\r\n'
2025-06-07 17:50:51,215 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:51.215989+00:00\r\n\r\n'
2025-06-07 17:50:51,218 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:51.218558+00:00\r\n\r\n'
2025-06-07 17:50:55,605 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:55.605705+00:00\r\n\r\n'
2025-06-07 17:50:59,155 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:50:59.155888+00:00\r\n\r\n'
2025-06-07 17:51:06,233 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:06.233686+00:00\r\n\r\n'
2025-06-07 17:51:06,234 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:06.234798+00:00\r\n\r\n'
2025-06-07 17:51:06,236 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:06.236126+00:00\r\n\r\n'
2025-06-07 17:51:10,618 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:10.618600+00:00\r\n\r\n'
2025-06-07 17:51:14,171 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:14.171633+00:00\r\n\r\n'
2025-06-07 17:51:21,232 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:21.232957+00:00\r\n\r\n'
2025-06-07 17:51:21,232 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:21.232957+00:00\r\n\r\n'
2025-06-07 17:51:21,233 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:21.233960+00:00\r\n\r\n'
2025-06-07 17:51:25,623 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:25.623305+00:00\r\n\r\n'
2025-06-07 17:51:29,188 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:29.188430+00:00\r\n\r\n'
2025-06-07 17:51:36,254 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:36.254753+00:00\r\n\r\n'
2025-06-07 17:51:36,254 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:36.254753+00:00\r\n\r\n'
2025-06-07 17:51:36,256 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:36.256024+00:00\r\n\r\n'
2025-06-07 17:51:40,643 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:40.643009+00:00\r\n\r\n'
2025-06-07 17:51:44,207 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:44.207297+00:00\r\n\r\n'
2025-06-07 17:51:51,273 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:51.273239+00:00\r\n\r\n'
2025-06-07 17:51:51,274 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:51.274280+00:00\r\n\r\n'
2025-06-07 17:51:51,274 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:51.274280+00:00\r\n\r\n'
2025-06-07 17:51:55,663 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:55.663465+00:00\r\n\r\n'
2025-06-07 17:51:59,217 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:51:59.217088+00:00\r\n\r\n'
2025-06-07 17:52:06,271 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:06.271486+00:00\r\n\r\n'
2025-06-07 17:52:06,271 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:06.271486+00:00\r\n\r\n'
2025-06-07 17:52:06,272 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:06.272892+00:00\r\n\r\n'
2025-06-07 17:52:10,681 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:10.681422+00:00\r\n\r\n'
2025-06-07 17:52:14,222 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:14.222474+00:00\r\n\r\n'
2025-06-07 17:52:21,273 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:21.273077+00:00\r\n\r\n'
2025-06-07 17:52:21,274 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:21.274075+00:00\r\n\r\n'
2025-06-07 17:52:21,274 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:21.274075+00:00\r\n\r\n'
2025-06-07 17:52:25,680 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:25.680141+00:00\r\n\r\n'
2025-06-07 17:52:29,231 - sse_starlette.sse - DEBUG - [sse.py:218] - ping: b': ping - 2025-06-07 09:52:29.231569+00:00\r\n\r\n'
