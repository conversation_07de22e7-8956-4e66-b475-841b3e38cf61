[tool:pytest]
# Pytest configuration for image_analyzer tests
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --asyncio-mode=auto
markers =
    asyncio: marks tests as async
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    slow: marks tests as slow running
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
