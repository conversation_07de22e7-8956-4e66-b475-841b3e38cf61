"""
简化的MCP Web Scraper Service - 只有一个主要接口
一次性返回网页文本和图片分析数据
"""
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from fastmcp import FastMCP
from config import get_settings
from web_scraper import WebScraper
from image_analyzer import DoubaoImageAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize settings
settings = get_settings()

# Create FastMCP server
mcp = FastMCP(
    name="Web Scraper with Vision Analysis",
    description="一次性抓取网页并分析图片的MCP服务"
)

@mcp.tool()
async def scrape_and_analyze(url: str, max_images: int = 5) -> Dict[str, Any]:
    """
    一次性抓取网页并分析图片，返回完整的文本和图片解析数据
    
    Args:
        url: 要抓取的网页URL
        max_images: 最大分析图片数量，默认5张
        
    Returns:
        包含网页文本、图片分析结果的完整数据
    """
    try:
        logger.info(f"开始抓取和分析: {url}")
        
        # 1. 抓取网页
        async with WebScraper() as scraper:
            scrape_result = await scraper.scrape_webpage(url)
            
        if not scrape_result['success']:
            return {
                'success': False,
                'url': url,
                'error': f"抓取失败: {scrape_result.get('error', '未知错误')}"
            }
        
        # 2. 分析图片
        image_analyses = []
        if scrape_result['images']:
            analyzer = DoubaoImageAnalyzer()
            
            # 限制分析的图片数量
            images_to_analyze = scrape_result['images'][:max_images]
            
            for img_info in images_to_analyze:
                if img_info.get('local_path'):
                    analysis = await analyzer.analyze_image(
                        img_info['local_path'],
                        prompt="详细描述这张图片的内容，包括主要元素、文字、颜色和布局。",
                        context=f"这张图片来自网页: {url}"
                    )
                    image_analyses.append({
                        'url': img_info['url'],
                        'local_path': img_info['local_path'],
                        'analysis': analysis
                    })
        
        # 3. 返回完整结果
        result = {
            'success': True,
            'url': url,
            'title': scrape_result['title'],
            'text_content': scrape_result['text_content'],
            'main_content': scrape_result['main_content'],
            'meta_description': scrape_result['meta_description'],
            'total_images': len(scrape_result['images']),
            'analyzed_images': len(image_analyses),
            'image_analyses': image_analyses,
            'timestamp': scrape_result['timestamp']
        }
        
        logger.info(f"完成分析 {url}: {len(scrape_result['text_content'])} 字符, {len(image_analyses)} 张图片已分析")
        return result
        
    except Exception as e:
        logger.error(f"抓取分析错误 {url}: {e}")
        return {
            'success': False,
            'url': url,
            'error': str(e)
        }

if __name__ == "__main__":
    logger.info("🚀 Starting Simple MCP Web Scraper Service...")
    logger.info(f"📍 Server URL: http://{settings.mcp_host}:{settings.mcp_port}/sse")
    logger.info(f"📁 Output directory: {settings.output_dir}")
    logger.info(f"🤖 Doubao model: {settings.doubao_model_id}")
    
    # Ensure output directory exists
    from config import ensure_output_dir
    ensure_output_dir()
    logger.info("✅ Output directory ready")
    
    # Test Doubao API configuration (optional)
    try:
        DoubaoImageAnalyzer()
        logger.info("✅ Doubao API configuration loaded")
    except Exception as e:
        logger.warning(f"⚠️ Doubao API configuration issue: {e}")
    
    logger.info("🎉 Starting server - ready to accept connections!")
    
    # Run with SSE transport
    mcp.run(
        transport="sse",
        host=settings.mcp_host,
        port=settings.mcp_port
    )
